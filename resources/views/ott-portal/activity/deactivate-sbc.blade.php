@extends('layouts.master')
@section('title')
    Update Deactivate SBC
@endsection
@section('css')
    <!-- Add any additional CSS here -->
    <link href="{{ URL::asset('/assets/libs/select2/select2.min.css') }}" rel="stylesheet" type="text/css" />
@endsection

@section('content')
    @component('common-components.breadcrumb')
        @slot('pagetitle') Activity Management @endslot
        @slot('title') Update Deactivate SBC @endslot
    @endcomponent

    <div class="row justify-content-center">
        <div class="col-md-8 col-sm-12">
            <div class="card p-md-4 py-md-3">
                <div class="card-body">
                    <h5 class="card-title mb-1">Update Deactivate SBC Activity</h5>
                    <small class="text-muted text-truncate mb-0"><i>Click 'Retry Termination' button to attempt SBC termination again or fill in all required (*) fields and click 'Update' button to skip termination</i></small>
                    <div class="mb-2 mb-md-3 mt-3">
                        <label for="activity_description" class="col-form-label">Activity Description</label>
                        <input class="form-control" type="text" placeholder="Activity Description" id="activity_description" name="activity_description" value="{{ $activity->activityType->description ?? '' }}" disabled>
                    </div>
                    <!-- <div class="mb-2 mb-md-3">
                        <label for="service_realm" class="col-form-label">Service Realm</label>
                        <input class="form-control" type="text" placeholder="Service Realm" id="service_realm" name="service_realm" value="{{ $activity->trackable->customer->brn ?? '' }}" disabled>
                    </div> -->
                    <div class="mb-2 mb-md-3">
                        <label for="package_and_phone_number" class="col-form-label">Service Details</label>
                        @php
                            $data = json_decode($activity->notes ?? '[]', true);
                        @endphp
                        <div class="table-responsive mb-0">
                            <table class="table table-bordered mb-0">
                                <thead>
                                    <tr>
                                        <th class="align-top" style="width: 30%">Package Name</th>
                                        <th class="align-top" style="width: 30%">Phone Numbers</th>
                                        <th class="align-top" style="width: 20%">Ingress Realm</th>
                                        <th class="align-top" style="width: 20%">Egress Realm</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($data as $item)
                                        <tr>
                                            <td>{{ $item['package'] ?? '' }}</td>
                                            <td>
                                                @foreach($item['phone_number'] ?? [] as $phone)
                                                    {{ $phone }}<br>
                                                @endforeach
                                            </td>
                                            <td>{{ $item['ingress_realm'] ?? '' }}</td>
                                            <td>{{ $item['egress_realm'] ?? '' }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="mb-2 mb-md-3 row mt-4">
                        <label for="activity_status" class="col-form-label col-md-2">*Activity Status</label>
                        <div class="col-md-10">
                            <select class="form-select" id="activity_status" name="activity_status">
                                <option value="in progress" {{ $activity->status === 'in progress' ? 'selected' : '' }}>In Progress</option>
                                <option value="done" {{ $activity->status === 'done' ? 'selected' : '' }}>Done</option>
                            </select>
                            <small id="activity_status_error" class="text-danger error-message" style="display: none"></small>
                        </div>
                    </div>

                    @if($activity->activityType->name === 'Exception - Deactivate SBC (Standard)')
                    <hr class="my-4">
                    <h5 class="card-title my-1">Termination History</h5>
                    <span class="badge bg-warning-subtle text-warning font-size-12">Display the log of SBC termination attempts</span>
                        <!-- Retry History Section -->
                        <div id="retry-history-content">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span class="ms-2">Loading termination history...</span>
                            </div>
                        </div>
                    @endif

                    @if($activity->activityType->name === 'Exception - Deactivate SBC (Others)')
                    <hr class="my-4">
                    <h5 class="card-title my-1">Manual Termination Required</h5>
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Offline Processing Required</h6>
                        <p class="mb-2">This SBC termination requires manual offline processing by the technical team.</p>
                        <ul class="mb-0">
                            <li>SBC configuration changes must be performed manually</li>
                            <li>No automated retry functionality is available</li>
                            <li>Mark as "Done" only after manual termination is completed</li>
                        </ul>
                    </div>
                    @endif

                </div>
            </div>
            <div class="d-flex justify-content-end mt-3">
                @if($activity->activityType->name === 'Exception - Deactivate SBC (Standard)' && $activity->status !== 'done')
                    <button type="button" class="btn btn-warning me-2" id="btn-retry-termination">
                        <i class="fas fa-redo me-2"></i>Retry Termination
                    </button>
                @endif
                <button type="button" class="btn btn-primary me-2" id="btn-update-deactivate-sbc">Update</button>
                <button type="button" class="btn btn-light" onclick="window.history.back();">Back</button>
            </div>

            @if($activity->activityType->name === 'Exception - Deactivate SBC (Standard)')
                <div class="alert alert-warning mt-3 mb-5">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>About SBC Termination Retries</h6>
                    <ul class="mb-0">
                        <li><strong>Automatic Retry:</strong> The system automatically attempts 1 retry if the initial termination fails.</li>
                        <li><strong>Manual Retries:</strong> You can retry termination unlimited times using the "Retry Termination" button.</li>
                        <li><strong>Activity Status:</strong> During retry, the activity status changes to "In Progress (Terminating)" and returns to "In Progress" if the retry fails.</li>
                        <li><strong>Success:</strong> When termination succeeds, activities are marked as "Done" automatically and services are deactivated.</li>
                        <li><strong>Manual Completion:</strong> You can manually mark the activity as "Done" if termination was completed outside the system.</li>
                    </ul>
                </div>
            @endif

            @if($activity->activityType->name === 'Exception - Deactivate SBC (Others)')
                <div class="alert alert-info mt-3 mb-5">
                    <h6><i class="fas fa-info-circle me-2"></i>Manual Termination Workflow</h6>
                    <ul class="mb-0">
                        <li><strong>Offline Processing:</strong> SBC termination for Others packages requires manual configuration changes.</li>
                        <li><strong>No Automated Retry:</strong> The "Retry Termination" button is not available for Others packages.</li>
                        <li><strong>Manual Completion:</strong> Change the status to "Done" and click "Update" only after manual termination is completed.</li>
                        <li><strong>Service Deactivation:</strong> Services will be deactivated automatically when you mark the activity as "Done".</li>
                    </ul>
                </div>
            @endif

            <!-- Failed Log Modal -->
            <div class="modal fade" id="failedLogModal" tabindex="-1" role="dialog" aria-labelledby="failedLogModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-xl" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title" id="failedLogModalLabel">Failed Log</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Error Message:</label>
                                <div class="alert alert-danger" id="failedLogErrorMessage"></div>
                            </div>
                            <div class="card">
                                <div class="card-body">
                                    <label class="form-label fw-bold">Log Content:</label>
                                    <pre id="failedLogContent" class="bg-light p-3" style="max-height: 500px; overflow-y: auto; border-radius: 0.375rem; font-size: 0.875rem; line-height: 1.4;">
                                        <!-- Log content will be populated by JavaScript -->
                                    </pre>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-light waves-effect" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Toast --}}
    <div class="position-fixed top-0 start-50 translate-middle-x p-3" style="z-index: 9999">
        <div id="success-toast" class="toast overflow-hidden" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="align-items-center text-white bg-success border-0">
                <div class="d-flex">
                    <div class="toast-body" id="success-toast-message">
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        </div>
    </div>
    <div class="position-fixed top-0 start-50 translate-middle-x p-3" style="z-index: 9999">
        <div id="failed-toast" class="toast overflow-hidden" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="align-items-center text-white bg-danger border-0">
                <div class="d-flex">
                    <div class="toast-body" id="failed-toast-message">
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('script')   
    <script>
        /**
         * Show failed log in modal
         */
        function showFailedLog(timestamp, errorMessage, jobUuid) {
            // Show loading state in modal
            $('#failedLogModalLabel').text('Failed Log - ' + new Date(timestamp).toLocaleString());
            $('#failedLogErrorMessage').text('Loading...');
            $('#failedLogContent').text('Loading log content...');
            $('#failedLogModal').modal('show');
            
            // Make AJAX request to get log content
            $.ajax({
                url: '/api/sbc-termination/jobs/' + jobUuid + '/log',
                type: 'GET',
                dataType: 'json',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    if (response.success && response.data && response.data.log_content) {
                        // Set error message
                        $('#failedLogErrorMessage').text(errorMessage);
                        
                        // Set log content
                        $('#failedLogContent').text(response.data.log_content);
                    } else {
                        $('#failedLogErrorMessage').text(errorMessage);
                        $('#failedLogContent').text('No log content available.');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Failed to load log content:', xhr.status, xhr.responseText, status, error);
                    $('#failedLogErrorMessage').text(errorMessage);
                    $('#failedLogContent').text('Failed to load log content. Error: ' + (xhr.responseJSON?.message || xhr.status));
                }
            });
        }

        $(document).ready(function() {

            // Enable CSRF Token for all ajax requests
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            var activity = @json($activity);
            console.log(activity['activity_type']['name']);

            // Check if activity contains 'Others' package = '(Others)' else '(Standard)'
            var packageType = activity['activity_type']['name'].includes('(Others)') ? ' (Others)' : ' (Standard)';

            $('.page-title').append(' ' + packageType);

            // Load termination history for Standard activities only
            @if($activity->activityType->name === 'Exception - Deactivate SBC (Standard)')
                loadTerminationHistory();
            @endif

             $('#btn-update-deactivate-sbc').on('click', function() {
                // Clear previous error messages
                $('.error-message').hide();

                // Get the package name and phone numbers from the table
                var notes = @json($activity->notes); // This is a JSON string
                var detailsArray = [];
                try {
                    detailsArray = JSON.parse(notes) || [];
                } catch (e) {
                    detailsArray = [];
                }
                console.log(detailsArray);

                // temporary disable the button and change the text
                $(this).text('Updating...').addClass('disabled');

                $.ajax({
                    url: "{{ route('activity.update.deactivate_sbc', $activity->id) }}",
                    type: 'POST',
                    data: {
                        _method: 'PATCH',
                        details: detailsArray,
                        activity_status: $('#activity_status').val(),
                    },
                    success: function(response) {
                        console.log(response);
                        if (response.success) {
                            $('#success-toast-message').text(response.message);
                            var toast = new bootstrap.Toast($('#success-toast')[0]);
                            toast.show();

                            setTimeout(function() {
                                window.location.href = "{{ route('activity.index') }}";
                            }, 1500);
                        }
                        $('#btn-update-deactivate-sbc').text('Update').removeClass('disabled');
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            console.log(xhr.responseJSON.errors);
                            var errors = xhr.responseJSON.errors;
                            var errorHtml = '<ul>';
                            $.each(errors, function(key, value) {
                                errorHtml += '<li>' + value[0] + '</li>';
                            });
                            errorHtml += '</ul>';
                            // create error message
                            $.each(errors, function(key, value) {
                                var errorElement = $('#' + key + '_error');
                                if (errorElement.length) {
                                    errorElement.text(value[0]).show();
                                    // Focus on the field with the error
                                    $('#' + key).focus();
                                } else {
                                    errorHtml += '<li>' + value[0] + '</li>';
                                }
                            });

                            // Show the toast
                            $('#failed-toast-message').html("Please fill in all required fields.");
                            var toastElement = $('#failed-toast')[0];  // jQuery object, need to access DOM element
                            var toast = new bootstrap.Toast(toastElement, {
                                autohide: true,     // Automatically hide after the delay
                                delay: 3000         // Time in milliseconds before the toast hides
                            });
                            toast.show(); 
                        } else {
                            console.log(xhr.responseJSON);
                            // Show the toast
                            $('#failed-toast-message').html("Failed to update. Please check and retry.");
                            var toastElement = $('#failed-toast')[0];  // jQuery object, need to access DOM element
                            var toast = new bootstrap.Toast(toastElement, {
                                autohide: true,     // Automatically hide after the delay
                                delay: 3000         // Time in milliseconds before the toast hides
                            });
                            toast.show(); 
                        }
                        $('#btn-update-deactivate-sbc').text('Update').removeClass('disabled');
                    }
                });
            });

            // Handle retry termination for Standard and Others activities
            $('#btn-retry-termination').on('click', function() {
                if (confirm('Are you sure you want to retry SBC termination? This will queue a new termination attempt and redirect you to the activity list.')) {
                    $(this).text('Processing...').addClass('disabled');

                    $.ajax({
                        url: '/api/sbc-termination/{{ $activity->id }}/retry',
                        type: 'POST',
                        success: function(response) {
                            if (response.success) {
                                $('#success-toast-message').html('SBC termination retry initiated successfully. Redirecting to activity list...');
                                var toastElement = $('#success-toast')[0];
                                var toast = new bootstrap.Toast(toastElement, {
                                    autohide: true,
                                    delay: 2000
                                });
                                toast.show();

                                // Redirect to activity index page after delay
                                setTimeout(function() {
                                    window.location.href = '{{ route("activity.index") }}';
                                }, 2000);
                            }
                        },
                        error: function(xhr, status, error) {
                            $('#failed-toast-message').html(xhr.responseJSON?.message || 'Failed to initiate termination retry');
                            var toastElement = $('#failed-toast')[0];
                            var toast = new bootstrap.Toast(toastElement, {
                                autohide: true,
                                delay: 3000
                            });
                            toast.show();
                            $('#btn-retry-termination').text('Retry Termination').removeClass('disabled');
                        }
                    });
                }
            });

            /**
             * Load termination history for the current activity
             */
            function loadTerminationHistory() {
                console.log('Loading termination history for activity ID: {{ $activity->id }}');

                // Show loading state
                $('#retry-history-content').html('<div class="text-center"><div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div><span class="ms-2">Loading termination history...</span></div>');

                $.ajax({
                    url: '/api/sbc-termination/{{ $activity->id }}/history',
                    type: 'GET',
                    dataType: 'json',
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    success: function(response) {
                        console.log('Termination history response:', response);
                        if (response.success && response.data) {
                            displayTerminationHistory(response.data);
                        } else {
                            console.log('No termination history data available');
                            $('#retry-history-content').html('<p class="text-muted mb-0">No termination history available.</p>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Failed to load termination history:', xhr.status, xhr.responseText, status, error);
                        var errorMsg = 'Failed to load termination history.';
                        if (xhr.status === 404) {
                            errorMsg += ' Activity not found.';
                        } else if (xhr.status === 401) {
                            errorMsg += ' Authentication required.';
                        } else if (xhr.status === 403) {
                            errorMsg += ' Access denied.';
                        } else {
                            errorMsg += ' Error: ' + xhr.status;
                        }
                        $('#retry-history-content').html('<p class="text-danger mb-0">' + errorMsg + '</p>');
                    }
                });
            }

            /**
             * Display termination history in a formatted table
             */
            function displayTerminationHistory(terminationData) {
                console.log('Displaying termination history:', terminationData);
                if (!terminationData.job || !terminationData.job.retry_history || terminationData.job.retry_history.length === 0) {
                    console.log('No termination history found');
                    $('#retry-history-content').html('<p class="text-muted mb-0">No termination attempts found.</p>');
                    return;
                }

                var html = '<div class="row mb-3 mt-4">';
                html += '<div class="col-md-6">';
                html += '<p class="mb-1"><span class="fw-medium">Manual Retry Attempts:</span> ' + terminationData.job.manual_retry_count + '</p>';
                html += '<p class="mb-1"><span class="fw-medium">Automatic Retry:</span> ' + (terminationData.job.automatic_retry_attempted ? 'Yes' : 'No') + '</p>';
                html += '</div>';
                html += '<div class="col-md-6">';
                html += '<p class="mb-1"><span class="fw-medium">Current Status:</span> ' + terminationData.job.status + '</p>';
                html += '<p class="mb-1"><span class="fw-medium">Last Updated:</span> ' + new Date(terminationData.job.updated_at).toLocaleString() + '</p>';
                html += '</div>';
                html += '</div>';

                html += '<div class="table-responsive">';
                html += '<table class="table table-bordered">';
                html += '<thead>';
                html += '<tr>';
                html += '<th>No.</th>';
                html += '<th>Type</th>';
                html += '<th>Status</th>';
                html += '<th>Timestamp</th>';
                html += '<th>Message</th>';
                html += '<th>View Log</th>'; // New column for View Log
                html += '</tr>';
                html += '</thead>';
                html += '<tbody>';

                terminationData.job.retry_history.forEach(function(attempt, index) {
                    html += '<tr>';
                    html += '<td>' + (index + 1) + '</td>';
                    html += '<td><span class="badge bg-' + (attempt.type === 'automatic' ? 'primary' : 'warning') + '">' + attempt.type + '</span></td>';
                    html += '<td><span class="badge bg-' + (attempt.result === 'success' ? 'success' : 'danger') + '">' + attempt.result + '</span></td>';
                    html += '<td>' + new Date(attempt.timestamp).toLocaleString() + '</td>';
                    html += '<td>' + (attempt.error || 'N/A') + '</td>';
                    // Add View Log button for failed attempts
                    // Check if job has log file path and attempt is failed
                    if (attempt.result === 'failed' && terminationData.job.uuid) {
                        html += '<td><a href="javascript:void(0);" class="px-1 text-secondary" onclick="showFailedLog(\'' + attempt.timestamp + '\', \'' + (attempt.error || 'N/A') + '\', \'' + terminationData.job.uuid + '\')" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="View Log"><i class="bx bx-file-find font-size-20"></i></a></td>';
                    } else {
                        html += '<td><span class="text-muted">N/A</span></td>';
                    }
                    html += '</tr>';
                });

                html += '</tbody>';
                html += '</table>';
                html += '</div>';

                $('#retry-history-content').html(html);
                // Initialize tooltips after rendering the table
                $('[data-bs-toggle="tooltip"]').tooltip();
            }
        });

    </script>
@endsection

# IME Rate Pricing Cancellation Enhancement for SBC Termination Workflow

**Date:** September 17, 2025  
**File:** `app/Listeners/HandleSbcTerminationCompletion.php`  
**Purpose:** Automatically manage IME rate pricing cancellation when a customer's last service is terminated

## Overview

This enhancement adds a multi-step validation and cancellation process to the SBC termination workflow that automatically handles IME rate pricing cancellation when a customer no longer has any active services.

## Implementation Requirements

### Step 1: Service Status Validation
- Query customer's services to check for active services
- Skip IME cancellation if active services exist
- Proceed to Step 2 if no active services found

### Step 2: Custom Price Entry Lookup
- Search `ime_rate_pricing` table for active custom pricing entries
- Filter by realm pattern and active status
- Skip if no active entries found

### Step 3: Create Cancellation Entry
- Duplicate active entry with cancellation modifications
- Set `effective_end_date` and `entry_status` = 'D'

### Step 4: Generate IME Rate Pricing File
- Create corresponding cancellation file using existing patterns

## Proposed Code Changes

### 1. Add Required Imports

```php
// Add to existing imports at the top of the file
use App\Models\ImeRatePricing;
use App\Models\Service;
use Carbon\Carbon;
```

### 2. Add New Method Call in Main Handler

```php
// Add after line 59 (after completeExistingActivities call)
// Process IME rate pricing cancellation if customer has no active services
$this->processImeRatePricingCancellation($order);
```

### 3. Add Service Status Validation Method

```php
/**
 * Step 1: Check if customer has any active services
 * Active services are those with status: 'in service', 'activated', or 'pending activation'
 */
private function hasActiveServices(Order $order): bool
{
    $customer = $order->customer;
    if (!$customer) {
        Log::warning('Customer not found for order', ['order_id' => $order->order_id]);
        return false;
    }

    $activeStatuses = ['in service', 'activated', 'pending activation'];
    
    $activeServicesCount = Service::where('customer_id', $customer->id)
        ->whereIn('status', $activeStatuses)
        ->count();

    Log::info('Active services check for IME rate pricing cancellation', [
        'order_id' => $order->order_id,
        'customer_id' => $customer->id,
        'customer_name' => $customer->name,
        'active_services_count' => $activeServicesCount,
        'checked_statuses' => $activeStatuses
    ]);

    return $activeServicesCount > 0;
}
```

### 4. Add Custom Price Entry Lookup Method

```php
/**
 * Step 2: Find active IME rate pricing entry for customer
 */
private function findActiveImeRatePricingEntry(Order $order): ?ImeRatePricing
{
    $customer = $order->customer;
    if (!$customer || !$customer->brn) {
        Log::warning('Customer or BRN not found for IME rate pricing lookup', [
            'order_id' => $order->order_id,
            'customer_id' => $customer->id ?? null
        ]);
        return null;
    }

    // Generate realm using same logic as provisioning
    $realm = 'tmims-' . $this->sanitizeBrn($customer->brn);
    
    $activeEntry = ImeRatePricing::where('realm', $realm)
        ->where('entry_status', 'A')
        ->latest()
        ->first();

    Log::info('IME rate pricing entry lookup', [
        'order_id' => $order->order_id,
        'customer_brn' => $customer->brn,
        'realm' => $realm,
        'active_entry_found' => !is_null($activeEntry),
        'entry_id' => $activeEntry->id ?? null
    ]);

    return $activeEntry;
}
```

### 5. Add BRN Sanitization Method

```php
/**
 * Sanitize BRN for use in filenames by replacing problematic characters
 * (Same implementation as HandleSbcProvisioningCompletion)
 */
private function sanitizeBrn(string $brn): string
{
    return str_replace(['/', '\\', ':', '*', '?', '"', '<', '>', '|'], '-', $brn);
}
```

### 6. Add Cancellation Entry Creation Method

```php
/**
 * Step 3: Create cancellation entry by duplicating active entry
 */
private function createImeRatePricingCancellationEntry(
    ImeRatePricing $activeEntry,
    Order $order
): ?ImeRatePricing {
    try {
        // Generate file sequence (same logic as provisioning)
        $latestImeRatePricing = ImeRatePricing::whereDate('created_at', Carbon::today())
            ->latest()
            ->first();
        $fileSequence = $latestImeRatePricing ?
            str_pad($latestImeRatePricing->file_sequence + 1, 3, '0', STR_PAD_LEFT) : '001';

        // Generate filename
        $filename = 'CYPHER_IME_' . now()->format('YmdHi') . '_' . $fileSequence . '.dat';

        // Create cancellation entry
        $cancellationEntry = ImeRatePricing::create([
            'realm' => $activeEntry->realm,
            'effective_start_date' => $activeEntry->effective_start_date,
            'effective_end_date' => now(), // Set end date to current timestamp
            'entry_status' => 'D', // Deactivated/cancelled
            'fixed_to_fixed' => $activeEntry->fixed_to_fixed,
            'fixed_to_mobile' => $activeEntry->fixed_to_mobile,
            'file_sequence' => $fileSequence,
            'filename' => $filename,
        ]);

        Log::info('Created IME rate pricing cancellation entry', [
            'order_id' => $order->order_id,
            'original_entry_id' => $activeEntry->id,
            'cancellation_entry_id' => $cancellationEntry->id,
            'realm' => $activeEntry->realm,
            'filename' => $filename,
            'effective_end_date' => now()->toDateTimeString()
        ]);

        return $cancellationEntry;

    } catch (\Exception $e) {
        Log::error('Failed to create IME rate pricing cancellation entry', [
            'order_id' => $order->order_id,
            'active_entry_id' => $activeEntry->id,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        return null;
    }
}
```

### 7. Add File Generation Method

```php
/**
 * Step 4: Generate IME rate pricing cancellation file
 */
private function createImeRatePricingCancellationFile(
    ImeRatePricing $cancellationEntry,
    Order $order
): void {
    try {
        // Create file content (same format as provisioning)
        $fileContent = sprintf(
            "%s,%s,%s,%s,%d,%d\n",
            $cancellationEntry->realm,
            $cancellationEntry->effective_start_date->format('YmdHis'),
            $cancellationEntry->effective_end_date->format('YmdHis'), // Include end date for cancellation
            $cancellationEntry->entry_status,
            $cancellationEntry->fixed_to_fixed,
            $cancellationEntry->fixed_to_mobile
        );

        // Create file path
        $filePath = public_path('mediation/upload/' . $cancellationEntry->filename);

        // Write file
        $file = fopen($filePath, 'w');
        if ($file === false) {
            throw new \Exception('Failed to open file for writing: ' . $filePath);
        }

        fwrite($file, $fileContent);
        fclose($file);

        // Set file permissions
        chmod($filePath, 0775);

        // Change file ownership (same as provisioning)
        shell_exec("sudo chown 1002:www-data " . escapeshellarg($filePath));

        Log::info('Created IME rate pricing cancellation file', [
            'order_id' => $order->order_id,
            'filename' => $cancellationEntry->filename,
            'file_path' => $filePath,
            'file_content' => trim($fileContent)
        ]);

    } catch (\Exception $e) {
        Log::error('Failed to create IME rate pricing cancellation file', [
            'order_id' => $order->order_id,
            'filename' => $cancellationEntry->filename,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        throw $e;
    }
}
```

### 8. Add Main Orchestration Method

```php
/**
 * Process IME rate pricing cancellation when customer has no active services
 * This method orchestrates the 4-step validation and cancellation process
 */
private function processImeRatePricingCancellation(Order $order): void
{
    try {
        Log::info('Starting IME rate pricing cancellation process', [
            'order_id' => $order->order_id,
            'customer_id' => $order->customer->id ?? null
        ]);

        // Step 1: Service Status Validation
        if ($this->hasActiveServices($order)) {
            Log::info('Customer has active services, skipping IME rate pricing cancellation', [
                'order_id' => $order->order_id,
                'customer_id' => $order->customer->id
            ]);
            return;
        }

        // Step 2: Custom Price Entry Lookup
        $activeEntry = $this->findActiveImeRatePricingEntry($order);
        if (!$activeEntry) {
            Log::info('No active IME rate pricing entry found, skipping cancellation', [
                'order_id' => $order->order_id,
                'customer_id' => $order->customer->id
            ]);
            return;
        }

        // Step 3: Create Cancellation Entry
        $cancellationEntry = $this->createImeRatePricingCancellationEntry($activeEntry, $order);
        if (!$cancellationEntry) {
            Log::error('Failed to create cancellation entry, aborting IME rate pricing cancellation', [
                'order_id' => $order->order_id,
                'active_entry_id' => $activeEntry->id
            ]);
            return;
        }

        // Step 4: Generate IME Rate Pricing File
        $this->createImeRatePricingCancellationFile($cancellationEntry, $order);

        // Log audit trail
        AuditService::logActivity(
            'created',
            get_class($cancellationEntry),
            $cancellationEntry->id,
            "Created IME rate pricing cancellation file {$cancellationEntry->filename} for customer {$order->customer->name} via automated SBC termination"
        );

        Log::info('IME rate pricing cancellation process completed successfully', [
            'order_id' => $order->order_id,
            'customer_id' => $order->customer->id,
            'cancellation_entry_id' => $cancellationEntry->id,
            'filename' => $cancellationEntry->filename
        ]);

    } catch (\Exception $e) {
        Log::error('IME rate pricing cancellation process failed', [
            'order_id' => $order->order_id,
            'customer_id' => $order->customer->id ?? null,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);

        // Don't throw the exception to avoid breaking the main termination completion flow
        // The termination was successful, IME cancellation is supplementary
    }
}
```

## Integration Points

### Database Transaction Integrity
- The IME rate pricing cancellation process is wrapped in try-catch blocks
- Failures in IME cancellation do not affect the main termination completion flow
- All database operations use proper error handling and logging

### Error Handling Strategy
- **Non-blocking errors**: IME cancellation failures are logged but don't break termination
- **Comprehensive logging**: Each step includes detailed logging for troubleshooting
- **Graceful degradation**: Missing customer data or BRN results in process skip, not failure

### Audit Trail
- All IME rate pricing cancellation entries are logged via AuditService
- File creation and database operations are tracked
- Customer service status checks are logged for transparency

## Business Logic Considerations

### Service Status Validation
- **Active statuses checked**: 'in service', 'activated', 'pending activation'
- **Case-insensitive comparison**: Uses `whereIn()` for exact matching
- **Customer-wide check**: Validates all services for the customer, not just order-specific services

### Realm Generation
- **Consistent pattern**: Uses same `tmims-{sanitized_brn}` format as provisioning
- **BRN sanitization**: Replaces filesystem-unsafe characters with hyphens
- **Error handling**: Missing BRN results in process skip with warning log

### File Generation
- **Consistent format**: Follows same CSV structure as provisioning files
- **Unique sequencing**: Uses daily sequence numbering like provisioning
- **File permissions**: Sets proper ownership and permissions for mediation system

## Testing Recommendations

### Unit Tests
1. **Service status validation**: Test with various service status combinations
2. **IME entry lookup**: Test realm generation and database queries
3. **Cancellation entry creation**: Test data duplication and modifications
4. **File generation**: Test file content format and permissions

### Integration Tests
1. **End-to-end workflow**: Test complete termination with IME cancellation
2. **Error scenarios**: Test missing customer data, BRN, or IME entries
3. **Transaction integrity**: Verify main termination succeeds even if IME fails
4. **Audit logging**: Verify all operations are properly logged

### Edge Cases
1. **Multiple active services**: Ensure cancellation is skipped appropriately
2. **Missing BRN**: Verify graceful handling of customers without BRN
3. **No active IME entries**: Ensure process skips without errors
4. **File system errors**: Test handling of file creation failures

## Deployment Considerations

### Prerequisites
- Ensure `mediation/upload/` directory exists and has proper permissions
- Verify database schema includes all required IME rate pricing fields
- Confirm AuditService is properly configured

### Monitoring
- Monitor logs for IME cancellation process execution
- Track file creation in mediation upload directory
- Monitor for any termination completion failures

### Rollback Plan
- Changes are additive and non-breaking
- Can be disabled by commenting out the method call in main handler
- No database schema changes required

---

**Note**: This implementation proposal provides a comprehensive solution for automatic IME rate pricing cancellation during SBC termination. The code follows existing patterns from `HandleSbcProvisioningCompletion.php` and maintains consistency with the current codebase architecture.

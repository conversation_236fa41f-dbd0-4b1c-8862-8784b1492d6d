<?php

namespace App\Http\Controllers;

use App\Http\Requests\CustomOrderStoreRequest;
use App\Models\Activity;
use App\Models\ActivityType;
use App\Models\Attachment;
use App\Models\Contract;
use App\Models\Customer;
use App\Models\Order;
use App\Models\Service;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use App\Models\PhoneNumberInventory;
use App\Models\ServiceItem;
use App\Models\ServiceHistory;
use App\Services\AuditService;
use App\Services\SbcProvisioningQueueService;
use App\Services\SbcTerminationQueueService;
use App\Models\SbcProvisioningJob;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;


class OrderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('ott-portal.order.index');
    }

    /**
     * Get Customer Info by BRN (calls NOVA, then ICP if needed)
     */
    public function getCustomerInfoBrn(string $customerIdNum = null)
    {
        // Decode the customerIdNum in case it was URL-encoded
        $customerIdNum = urldecode($customerIdNum);
        
        if (!$customerIdNum) {
            return response()->json([
                'success' => false,
                'message' => 'customerIdNum is required'
            ], 400);
        }

        try {
            $tmoipApiService = app(\App\Services\Api\TmoipApiService::class);

            // 1st API call: NOVA
            $novaResponse = $tmoipApiService->queryCustomerInfoNova($customerIdNum);

            if (
                isset($novaResponse['eventName']) &&
                $novaResponse['eventName'] === 'evOQueryCustomerInfoNOVA' &&
                (empty($novaResponse['queryCustomerInfoNovaRes']) || $novaResponse['queryCustomerInfoNovaRes'] === null)
            ) {
                // 2nd API call: ICP
                $icpResponse = $tmoipApiService->queryCustomerInfoIcp($customerIdNum);

                if (
                    isset($icpResponse['eventName']) &&
                    $icpResponse['eventName'] === 'evOQueryCustomerInfoICP' &&
                    (empty($icpResponse['queryCustomerInfoIcpRes']) || $icpResponse['queryCustomerInfoIcpRes'] === null)
                ) {
                    // Both calls returned null
                    return response()->json([
                        'success' => false,
                        'message' => 'No customer info found in both NOVA and ICP',
                        'nova' => $novaResponse,
                        'icp' => $icpResponse
                    ]);
                } else {
                    // ICP call succeeded
                    return response()->json([
                        'success' => true,
                        'source' => 'ICP',
                        'data' => $icpResponse
                    ]);
                }
            } else {
                // NOVA call succeeded
                return response()->json([
                    'success' => true,
                    'source' => 'NOVA',
                    'data' => $novaResponse
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error occurred while fetching customer info',
                'error' => $e->getMessage()
            ], 500);
        }
    }


    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)    
    {
        try {
            if ($id == 'all') {
                // Query order table
                $orders = Order::with(['quote', 'quote.quote_items', 'customer', 'sof', 'updater', 'creator'])->get();

                // Format data to be displayed in the datatable
                $data = $orders->map(function ($order) {
                    // Assign package user to variable
                    $package_user = $order->quote->quote_items->map(function ($quote_item) {
                        if ($quote_item->quotable_type == 'App\Models\Package') {
                            return $quote_item->description . ': ' . $quote_item->quantity . ' users';
                        }
                    })->filter()->implode('<br />');

                    // Assign action to variable
                    if (in_array(strtolower($order->status), ['pending', 'draft'])) {
                        $action = "<a href='" . route('order.edit', ['order' => $order->id]) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Edit'><i class='bx bx-edit-alt font-size-20'></i></a>";
                    } 
                    // Just submit the order either SBC or license has been done
                    else if (strtolower($order->status) == 'in progress') {
                        $action = "
                        <a href='" . route('order.view', ['order' => $order->id]) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='View'><i class='bx bx-file-find font-size-20'></i></a>
                        <a href='". route('order.cancel', ['order' => $order->id]) ."' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Cancel'><i class='bx bx-x-circle font-size-20'></i></a>
                        ";
                    } 
                    // Both SBC and license has been done
                    else if (strtolower($order->status) == 'service activated') {
                        $action = "
                        <a href='" . route('order.view', ['order' => $order->id]) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='View'><i class='bx bx-file-find font-size-20'></i></a>
                        <a href='". route('order.cancel', ['order' => $order->id]) ."' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Cancel'><i class='bx bx-x-circle font-size-20'></i></a>
                        <a href='" . route('order.upload.document', ['order' => $order->id]) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Upload Document'><i class='bx bx-upload font-size-20'></i></a>
                        ";
                    } 
                    // UAT & COA has been uploaded
                    else if (strtolower($order->status) == 'service tested') {
                        $action = "
                        <a href='" . route('order.view', ['order' => $order->id]) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='View'><i class='bx bx-file-find font-size-20'></i></a>
                        <a href='" . route('order.upload.document', ['order' => $order->id]) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Upload Document'><i class='bx bx-upload font-size-20'></i></a>
                        ";
                    } 
                    // Both PO & Acceptance Form has been uploaded
                    else if (in_array(strtolower($order->status), ['completed', 'cancelled', 'completed rollback'])) {
                        $action = "
                        <a href='" . route('order.view', ['order' => $order->id]) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='View'><i class='bx bx-file-find font-size-20'></i></a>
                        ";
                    } 
                    else $action = "";

                    return [
                        'id' => $order->id,
                        'quote_id' => "<a href='" . route('quote.view', $order->quote->id) . "' data-bs-toggle='tooltip' data-bs-placement='bottom' data-bs-title='View Quote' target='_blank'>" . $order->quote->quote_id . "</a>",
                        'order_id' => $order->order_id,
                        'sof_id' => "<a href='" . route('sof.show', $order->sof->id) . "' data-bs-toggle='tooltip' data-bs-placement='bottom' data-bs-title='View SOF' target='_blank'>" . $order->sof->sof_id . "</a>",
                        'customer' => $order->customer->name ?? $order->quote->customer_name,
                        'order_type' => $order->order_type,
                        'package_user' => $package_user,
                        'contract_length' => $order->quote->contract_length,
                        'status' => "<span class='badge " . 
                            (in_array(strtolower($order->status), ['draft' , 'pending']) ? 'bg-dark-subtle' : 
                            (strtolower($order->status) == 'in progress' ? 'bg-warning-subtle text-warning' :
                            (in_array(strtolower($order->status), ['service activated', 'service tested']) ? 'bg-info-subtle text-info' :  
                            (in_array(strtolower($order->status), ['completed', 'completed rollback']) ? 'bg-success-subtle text-success' : 
                            (str_contains(strtolower($order->status), 'cancelled') ? 'bg-danger-subtle text-danger' :''))))) .
                            " font-size-12'>" . $order->status . "</span>",
                        // 'activation_date' => $order->activation_date ? $order->activation_date->format('d-m-Y H:i') : null,
                        // 'special_instruction' => $order->special_instruction,
                        // 'created_at' => $order->created_at->format('d-m-Y H:i'),
                        'updated_at' => $order->updated_at->format('d-m-Y H:i'),
                        // 'created_by' => $order->creator ? $order->creator->name : 'Chyper',
                        'updated_by' => $order->updater ? $order->updater->name : 'Chyper', 
                        'action' => $action,
                    ];
                });
            }

            return response()->json(compact('data'));

        } catch (\Throwable $th) {
            throw $th;
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $order = Order::with(['quote', 'quote.quote_items', 'sof', 'updater', 'creator', 'address'])->findOrFail($id);
        foreach ($order->address as $address) {
            $state = \App\Models\State::where('name', $address['state'])->first();
            $address->state = $state;
        }

        // dd($order->customer->address);

        // Logic on address
        // For new customer, the address under customer profile will be the same as the address tag to the order
        // For existing customer, the address under customer profile will be using the address tag to the order as billing address (primary = True), the first address in the assigned phone number will be address from the quote and the rest will be all address tag to the customer profile

        // Query customer table match from brn get from quote table and assign to variable
        $customer = Customer::with('addresses', 'addresses.state')->where('brn', $order->quote->brn)->first();
        if ($customer) {
            // Assign customer type
            $customer->customer_type = 'existing';
            $order->customer = $customer;
        } else {
            // Assign customer type
            $customer = new Customer();
            $customer->customer_type = 'new';
            $customer->name = $order->quote->customer_name;
            $customer->person_in_charge = $order->quote->person_in_charge;
            $customer->position_title = $order->quote->position_title;
            $customer->department = $order->quote->department;
            $customer->contact_no = $order->quote->contact_no;
            $customer->sfdc_id = $order->quote->sfdc_id;
            $customer->brn = $order->quote->brn;
            // $customer->email = $order->quote->recipient_email;
            // $customer->address = $order->quote->unit_street . ', ' . $order->quote->housing_area . ', ' . $order->quote->postcode . ', ' . $order->quote->city . ', ' . $order->quote->state;
            $customer->segment_group = '';
            $customer->segment_sub_group = '';
            $customer->segment_code = '';
            $customer->account_no = static::generateUniqueAccountNo();
            $order->customer = $customer;
        }

        // testing purpose 
        // dd($order->customer);

        // Get List of Value from file orderLovs.json
        $lov = json_decode(Storage::get('app/public/data/orderLov.json'), true);

        // dd($order->customer);
        return view('ott-portal.order.edit', compact('order', 'lov'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(CustomOrderStoreRequest $request, Order $order)
    {

        // dd($request->all());

        try {

            DB::beginTransaction();

            $validatedData = $request->validated();

            foreach ($request->tad as $staff) {
                // Extract name and email using regex or string splitting
                if (preg_match('/^(.*) \((.*)\)$/', $staff, $matches)) {
                    $user = User::whereRaw('LOWER(email) = ?', [trim(strtolower($matches[2]))])->first();
                    $teamMembers[] = [
                        'user_id' => $user ? $user->id : null,
                        'name' => trim($matches[1]),
                        'email' => trim($matches[2]),
                        'role' => 'TAD'
                    ];
                }
            }

            // Testing Data
            // dd($validatedData);

            // Task to update order
            // 1. Create customer if customer_type is new else skip
            // 2. Create new address (include billing and additional address) for new customer
            // 3. Update order - customer_id, order_type, status = in progress, update project team (remove existing user with role = TAD and add new user with role = TAD)
            // 4. Based on the assigned details, create new address and update phone number status = reserved, create new service contains - service id = phone number, customer_id, address_id, phone_number_id, status, activation_date, created_by, updated_by and create new service history - service_id, quote_id, order_id, sof_id, change_type (new service, modify, cancel, terminate), effective date, created_by, updated_by

            // Execute Task 1 - Create customer
            if ($validatedData['customer_type'] == 'new') {
                $customer = Customer::create([
                    'vertical' => $order->quote->vertical_id == 17 ? $order->quote->vertical_others : $order->quote->vertical->name,
                    'sales_segment' => $order->quote->sale_segment_id == 12 ? $order->quote->sale_segment_others : $order->quote->sale_segment->name,
                    'name' => $validatedData['customer_name'],
                    'person_in_charge' => $validatedData['person_in_charge'],
                    'position_title' => $validatedData['position_title'],
                    'department' => $validatedData['department'],
                    'contact_no' => $validatedData['contact_no'],
                    'sfdc_id' => $validatedData['sfdc_id'],
                    'brn' => $validatedData['brn'],
                    // 'email' => $validatedData['email'],
                    // 'address' => $validatedData['unit_street'] . ', ' . $validatedData['housing_area'] . ', ' . $validatedData['postcode'] . ', ' . $validatedData['city'] . ', ' . $validatedData['state'],
                    'segment_group' => $validatedData['segment_group'],
                    'segment_sub_group' => $validatedData['segment_sub_group'],
                    'segment_code' => $validatedData['segment_code'],
                    'account_no' => $validatedData['account_number'],
                ]);

                // Log Activity - Create new customer
                AuditService::logActivity(
                    'created',
                    'App\Models\Customer',
                    $customer->id,
                    'Created new customer: ' . $validatedData['customer_name']
                );
            } else {
                // Skip creating customer
                // Get customer id from brn
                $customer = Customer::where('brn', $validatedData['brn'])->first();
            }

            // Execute Task 2 - Create new address
            if ($validatedData['customer_type'] == 'new') {
                // Check if unit_street and housing area of general address = billing address
                if ($validatedData['unit_street'] == $validatedData['billing_unit_street'] && $request->housing_area == $request->billing_housing_area) {
                    // Create new address
                    $address = $customer->addresses()->create([
                        'unit_street' => $validatedData['unit_street'],
                        'housing_area' => $request->housing_area,
                        'postcode' => $validatedData['postcode'],
                        'city' => $validatedData['city'],
                        'state' => $validatedData['state'],
                        'is_primary' => true,
                    ]);

                    // Log Activity - Create new address
                    AuditService::logActivity(
                        'created',
                        'App\Models\Address',
                        $address->id,
                        'Created new address for customer ' . $validatedData['customer_name']
                    );
                }
                else {
                    // Create new address
                    $address = $customer->addresses()->create([
                        'unit_street' => $validatedData['unit_street'],
                        'housing_area' => $request->housing_area,
                        'postcode' => $validatedData['postcode'],
                        'city' => $validatedData['city'],
                        'state' => $validatedData['state'],
                        'is_primary' => false,
                    ]);

                    // Log Activity - Create new address
                    AuditService::logActivity(
                        'created',
                        'App\Models\Address',
                        $address->id,
                        'Created new address for customer ' . $validatedData['customer_name']
                    );

                    // Create billing address
                    $billing_address = $customer->addresses()->create([
                        'unit_street' => $validatedData['billing_unit_street'],
                        'housing_area' => $request->billing_housing_area,
                        'postcode' => $validatedData['billing_postcode'],
                        'city' => $validatedData['billing_city'],
                        'state' => $validatedData['billing_state'],
                        'is_primary' => true,
                    ]);

                    // Log Activity - Create new address
                    AuditService::logActivity(
                        'created',
                        'App\Models\Address',
                        $billing_address->id,
                        'Created new billing address for customer: ' . $validatedData['customer_name']
                    );
                }
            }

            // Execute Task 3 - Update order
            // Filter out existing TAD members and merge with new ones
            $currentTeam = array_filter(json_decode($order->project_team, true) ?? [], function($member) {
                return $member['role'] !== 'TAD';
            });
            
            $order->update([
                'customer_id' => $customer->id,
                'order_type' => $validatedData['order_type'],
                'status' => 'in progress',
                'updated_by' => auth()->id(),
                'project_team' => json_encode(array_merge($currentTeam, $teamMembers)),
            ]);

            // Log Activity - Update order
            AuditService::logActivity(
                'updated',
                'App\Models\Order',
                $order->id,
                'Updated order ' . $order->order_id
            );

            // Execute Task 4 - Create service, create address without address_id (if user add the address manually during tel no assignment), and update phone number status = reserved
            foreach($request->assigned_detail as $detail) {
                // Create new address
                // Check if unit_street and housing area and postcode not exist in all customer addresses
                // Get customer addresses
                $address = $customer->addresses()->whereRaw('LOWER(unit_street) = ?', [strtolower(rtrim($detail['unit_street'], ','))])
                    ->whereRaw('LOWER(housing_area) = ?', [strtolower(rtrim($detail['housing_area'], ','))])
                    ->where('postcode', $detail['postcode'])
                    ->whereRaw('LOWER(city) = ?', [strtolower($detail['city'])])
                    ->whereRaw('LOWER(state) = ?', [strtolower($detail['state'])])
                    ->first();

                if (!$address) {
                    $address = $customer->addresses()->create([
                        'unit_street' => $detail['unit_street'],
                        'housing_area' => $detail['housing_area'],
                        'postcode' => $detail['postcode'],
                        'city' => $detail['city'],
                        'state' => $detail['state'],
                        'is_primary' => false,
                    ]);
                }

                // Create new service, serviceItem, and update phone number status
                foreach($detail['phone_number'] as $phone_number) {

                    // Get Phone Number from Inventory
                    $phone_number_inventory = PhoneNumberInventory::where('phone_number', $phone_number)->first();

                    $service = Service::create([
                        'service_id' => $this->generateUniqueServiceId($phone_number),
                        'customer_id' => $customer->id,
                        'address_id' => $address->id,
                        'phone_number_id' => $phone_number_inventory->id,
                        'status' => 'pending activation',
                        // TO CONFIRM IF ACTIVITY CONF. OGG LICENSE NEEDED FROM PACKAGE SBCAAS OTHER
                        // 'license_status' => !str_contains(strtolower($detail['package']), 'other') ? 'pending activation' : 'not required',
                        'license_status' => 'pending activation',
                        'activation_date' => null,
                        'created_by' => auth()->id(),
                        'updated_by' => auth()->id(),
                    ]);

                    // Log Activity - Create new service
                    AuditService::logActivity(
                        'created',
                        'App\Models\Service',
                        $service->id,
                        'Created new service ' . $service->service_id
                    );

                    $service_history = ServiceHistory::create([
                        'service_id' => $service->id,
                        'quote_id' => $order->quote->id,
                        'order_id' => $order->id,
                        'sof_id' => $order->sof->id,
                        'change_type' => 'created',
                        'effective_date' => now(),
                        'created_by' => auth()->id(),
                        'updated_by' => auth()->id(),
                    ]);

                    // Log Activity - Create new service history
                    AuditService::logActivity(
                        'created',
                        'App\Models\ServiceHistory',
                        $service_history->id,
                        'Created new service history for service: ' . $phone_number
                    );

                    // Get quote item
                    $quote_item = $order->quote->quote_items()->where('description', $detail['package'])->first();

                    $provider = $quote_item->provider;

                    // Create service item for package
                    $serviceItem = ServiceItem::create([
                        'service_id' => $service->id,
                        'serviceable_id' => $quote_item->quotable_id,
                        'serviceable_type' => $quote_item->quotable_type,
                        'price_id' => $quote_item->price_id,
                        'price' => $quote_item->quoted_price,
                        'provider' => $provider,
                        'description' => $quote_item->description,
                    ]);

                    // Get related quote items (subpackages) for the same provider
                    $quote_items = $order->quote->quote_items()
                        ->where('description', 'like', '%' . $provider . '%')
                        ->where('quotable_type', 'App\Models\Subpackage')
                        ->get();
                    
                    // Check if $quote_items is empty
                    if ($quote_items->isEmpty()) {
                        $quote_items = $order->quote->quote_items()
                        ->where('description', 'like', '%other%')
                        ->where('quotable_type', 'App\Models\Subpackage')
                        ->get();
                    }

                    // Create service item for each subpackage
                    foreach($quote_items as $sub_item) {
                        ServiceItem::create([
                            'service_id' => $service->id,
                            'serviceable_id' => $sub_item->quotable_id,
                            'serviceable_type' => $sub_item->quotable_type,
                            'price_id' => $sub_item->price_id,
                            'price' => $sub_item->quoted_price,
                            'provider' => $sub_item->provider,
                            'description' => $sub_item->description,
                        ]);
                    }

                    // Update phone number - status
                    $phone_number_inventory->update([
                        'status' => 'reserved',
                    ]);

                    // Log Activity - Update phone number
                    AuditService::logActivity(
                        'updated',
                        'App\Models\PhoneNumberInventory',
                        $phone_number_inventory->id,
                        'Updated phone number '.$phone_number.' status to reserved'
                    );
                }
            }

            // Update create order activity - status and date
            $activity = Activity::where('trackable_id', $order->id)
                        ->where('trackable_type', get_class($order))
                        ->whereHas('activityType', function ($query) {
                            $query->where('name', 'Create Order');
                        })->where('status', 'in progress')->first();
            $activity->status = 'Done';
            $activity->actual_end_date = now();
            $activity->actual_duration = $activity->actual_end_date->diffInDaysFiltered(function (Carbon $date) {
                return !$date->isWeekend();
            }, $activity->actual_start_date);
            $activity->is_overdue = ($activity->actual_duration - $activity->activityType->duration > 0) ? true : false; 
            $activity->user_id = auth()->user()->id;
            $activity->save();

            // Create activity logs
            AuditService::logActivity(
                'updated',
                get_class($activity),
                $activity->id,
                'Activity '. $activity->activityType->name . ' status is changed to Done'
            );

            // Crete a new activity - Configure OTT license with partners
            $activityType = ActivityType::where('name', 'Configure OTT License')->first();

            // Calculate planned_end_date considering only working days
            $plannedEndDate = Carbon::now();
            $daysToAdd = $activityType->duration;
            while ($daysToAdd > 0) {
                $plannedEndDate->addDay();
                if (!$plannedEndDate->isWeekend()) {
                    $daysToAdd--;
                }
            }

            $activity = Activity::create([
                'activity_id' =>  (string) Activity::generateUniqueActivityId(),
                'activity_type_id' => $activityType->id,
                'trackable_id' => $order->id,
                'trackable_type' => get_class($order),
                'status' => 'in progress',
                'planned_start_date' => now(),
                'planned_end_date' => $plannedEndDate,
                'actual_start_date' => now(),
                'actual_end_date' => null,
                'actual_duration' => null,
                'aging' => null,
                'is_overdue' => false,
                'user_id' => null,
                'notes' => json_encode(collect($request->assigned_detail)
                    ->groupBy('package')
                    // TO CONFIRM IF ACTIVITY CONF. OGG LICENSE NEEDED FROM PACKAGE SBCAAS OTHER
                    // ->reject(function($group, $package) {
                    //     return stripos($package, 'other') !== false;
                    // })
                    ->map(function($group, $package) use ($validatedData, $order) {
                        // Extract FQDN, Service IP, and Public IP from the first item in the group if available
                        $firstItem = $group->first();
                        
                        // Get FQDN, service IP, and public IP from the first item in the group
                        $fqdn = isset($firstItem['fqdn']) ? $firstItem['fqdn'] : null;
                        $serviceIp = isset($firstItem['service_ip']) ? $firstItem['service_ip'] : null;
                        $publicIp = isset($firstItem['public_ip']) ? $firstItem['public_ip'] : null;
                        
                        return [
                            'ingress_realm' => stripos($package, 'teams') !== false ? "teams-".$this->sanitizeBrn($validatedData["brn"]) : 
                                          (stripos($package, 'zoom') !== false ? "zoom-".$this->sanitizeBrn($validatedData["brn"]) : 
                                          (stripos($package, 'webex') !== false ? "webex-".$this->sanitizeBrn($validatedData["brn"]) : 
                                          (stripos($package, 'other') !== false ? strtolower($order->quote->quote_items()->where('description', $package)->first()->provider)."-".$this->sanitizeBrn($validatedData["brn"]) : null))),
                            'egress_realm' => "tmims-".$this->sanitizeBrn($validatedData["brn"]),
                            'package' => $package,
                            'phone_number' => $group->flatMap(function($detail) {
                                return $detail['phone_number'];
                            })->values()->toArray(),
                            // Add FQDN, Service IP, and Public IP to the notes if available
                            'fqdn' => $fqdn,
                            'service_ip' => $serviceIp,
                            'public_ip' => $publicIp
                        ];
                    })->values()->toArray()),
                'updated_at' => now()->addMinutes(1)
            ]);

            // Create activity logs
            AuditService::logActivity(
                'created',
                get_class($activity),
                $activity->id,
                'Activity ' . $activity->activityType->name . ' is created'
            );

            // ============================================================================
            // ASYNCHRONOUS SBC PROVISIONING WITH INTELLIGENT ACTIVITY CREATION
            // ============================================================================

            // DEBUG: Log that we're entering the new SBC logic
            Log::info('🔧 ENTERING NEW ASYNCHRONOUS SBC PROVISIONING LOGIC', [
                'order_id' => $order->order_id,
                'assigned_detail_count' => count($request->assigned_detail ?? [])
            ]);

            $sbcStatus = ['action' => 'none', 'message' => 'No SBC provisioning required', 'job_uuid' => null];

            try {
                // Analyze customer packages to determine SBC provisioning strategy
                Log::info('🔍 ANALYZING CUSTOMER PACKAGES', [
                    'order_id' => $order->order_id,
                    'assigned_detail' => $request->assigned_detail
                ]);

                $packageAnalysis = $this->analyzeCustomerPackages($request->assigned_detail, $validatedData, $order);

                Log::info('📊 PACKAGE ANALYSIS RESULT', [
                    'order_id' => $order->order_id,
                    'strategy' => $packageAnalysis['strategy'],
                    'standard_count' => count($packageAnalysis['standard']),
                    'others_count' => count($packageAnalysis['others'])
                ]);

                switch ($packageAnalysis['strategy']) {
                    case 'others_only':
                        // Scenario 1: Only "others" packages - Skip SBC, create immediate activity
                        $this->createSbcExceptionActivity($order, $packageAnalysis['others'], 'others_only', $validatedData);
                        $sbcStatus = [
                            'action' => 'immediate_activity',
                            'message' => 'Manual SBC configuration required for others packages',
                            'job_uuid' => null
                        ];
                        break;

                    case 'standard_only':
                        // Scenario 2: Only standard packages - Queue SBC provisioning
                        $queueResult = $this->queueSbcProvisioning($order, $packageAnalysis['standard']);
                        $sbcStatus = [
                            'action' => $queueResult['success'] ? 'queued' : 'immediate_activity',
                            'message' => $queueResult['message'],
                            'job_uuid' => $queueResult['job_uuid'] ?? null
                        ];

                        if (!$queueResult['success']) {
                            // Fallback: Create immediate activity if queueing fails
                            $this->createSbcExceptionActivity($order, $packageAnalysis['all'], 'queue_failed', $validatedData);
                        }
                        break;

                    case 'mixed_packages':
                        // Scenario 3: Mixed packages - Queue SBC for standard, prepare for conditional activity
                        $queueResult = $this->queueSbcProvisioning($order, $packageAnalysis['standard']);
                        $sbcStatus = [
                            'action' => $queueResult['success'] ? 'queued' : 'immediate_activity',
                            'message' => $queueResult['message'],
                            'job_uuid' => $queueResult['job_uuid'] ?? null
                        ];

                        if (!$queueResult['success']) {
                            // Fallback: Create immediate activity with all packages if queueing fails
                            $this->createSbcExceptionActivity($order, $packageAnalysis['all'], 'queue_failed', $validatedData);
                        }
                        break;

                    default:
                        // Fallback: Create immediate activity for unknown scenarios
                        $this->createSbcExceptionActivity($order, $packageAnalysis['all'], 'unknown_scenario', $validatedData);
                        $sbcStatus = [
                            'action' => 'immediate_activity',
                            'message' => 'Unknown package configuration, manual intervention required',
                            'job_uuid' => null
                        ];
                }

                // Store package analysis for event listeners
                if (isset($sbcStatus['job_uuid']) && $sbcStatus['job_uuid'] !== null) {
                    Log::info('💾 UPDATING ORDER WITH SBC DATA', [
                        'order_id' => $order->order_id,
                        'job_uuid' => $sbcStatus['job_uuid'],
                        'strategy' => $packageAnalysis['strategy']
                    ]);

                    $updateResult = $order->update([
                        'sbc_job_uuid' => $sbcStatus['job_uuid'],
                        'sbc_package_analysis' => $packageAnalysis
                    ]);

                    Log::info('📝 ORDER UPDATE RESULT', [
                        'order_id' => $order->order_id,
                        'update_result' => $updateResult,
                        'job_uuid_to_save' => $sbcStatus['job_uuid']
                    ]);

                    // Refresh the order to get the latest data
                    $order->refresh();

                    Log::info('✅ ORDER UPDATED WITH SBC DATA', [
                        'order_id' => $order->order_id,
                        'sbc_job_uuid' => $order->sbc_job_uuid,
                        'sbc_package_analysis' => $order->sbc_package_analysis ? 'SET' : 'NULL'
                    ]);
                } else {
                    Log::info('⚠️ NO SBC JOB UUID TO STORE', [
                        'order_id' => $order->order_id,
                        'sbc_status' => $sbcStatus
                    ]);
                }

            } catch (\Exception $e) {
                // Fallback: Create immediate activity on any error
                Log::error('SBC provisioning analysis failed', [
                    'order_id' => $order->order_id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                $this->createSbcExceptionActivity($order, $request->assigned_detail, 'system_error', $validatedData);
                $sbcStatus = [
                    'action' => 'immediate_activity',
                    'message' => 'System error occurred, manual intervention required',
                    'job_uuid' => null
                ];
            }

            // ============================================================================
            // END ASYNCHRONOUS SBC PROVISIONING
            // ============================================================================


            DB::commit();

            // Redirect with a success message including SBC status
            return response()->json([
                'success' => true,
                'message' => 'Order (' . $order->order_id . ') is successfully submitted.',
                'sbc_status' => $sbcStatus ?? [
                    'action' => 'none',
                    'message' => 'No SBC provisioning required',
                    'job_uuid' => null
                ]
            ]);

        } catch (\Exception $error) {
            DB::rollBack(); // Rollback transaction on failure

            // Handle exceptions and redirect back with error message
            return response()->json([
                'success' => false,
                'message' => 'Error submitting order',
                'error' => $error->getMessage(),
                // add error line and file
                'line' => $error->getLine(),
                'file' => $error->getFile(),
            ], 500);
        } 

        // Process the validated data
        // $order->update([
        //     'order_type' => $validatedData['order_type'],
        //     'special_instruction' => $validatedData['special_instruction'] ?? null,
        // ]);

        // Handle customer and address updates here if needed
        // ...

        return response()->json([
            'status' => 'success',
            'message' => 'Order updated successfully.',
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    /**
     * Generate a unique order ID
     * Format: OR- + random numbers
     */
    public static function generateUniqueAccountNo(): string 
    {
        do {
            // Generate a random 10-digit number
            $number = str_pad(mt_rand(0, **********), 10, '0', STR_PAD_LEFT);
            $accountNo = 'CA-' . $number;
            
            // Check if the generated ID already exists
            $exists = Customer::where('account_no', $accountNo)->exists();
        } while ($exists);

        return $accountNo;
    }

    public function view(string $id)
    {
        $order = Order::with(['quote', 'quote.quote_items', 'sof', 'updater', 'creator', 'customer', 'customer.addresses', 'customer.addresses.state', 'latestServices', 'latestServices.serviceItems', 'latestServices.address', 'latestServices.address.state', 'fqdns', 'sipInterfaceIpAddresses', 'sipInterfacePublicIps'])->findOrFail($id);


        // Get List of Value from file orderLovs.json
        $lov = json_decode(Storage::get('app/public/data/orderLov.json'), true);

        // Group services by package and provider
        $datatable = $order->latestServices->flatMap(function($service) {
            // Get only package service items
            return $service->serviceItems->where('serviceable_type', 'App\Models\Package')->map(function($item) use ($service) {
                return [
                    'item' => $item,
                    'service' => $service
                ];
            });
        })->groupBy('item.provider')->map(function($items, $provider) use ($order) {
            $firstItem = $items->first();
            $packageDescription = $firstItem['item']->description;
            $packageName = strtolower($packageDescription);

            // Map package description to FQDN package enum
            $fqdnPackageType = $this->mapPackageNameToFqdnPackage($packageDescription);

            // Get FQDN for this package
            $fqdn = null;
            if ($fqdnPackageType) {
                $fqdn = $order->fqdns->where('package', $fqdnPackageType)->first();
            }

            // Get Service IP and Public IP for Zoom packages only
            $serviceIp = null;
            $publicIp = null;
            if (stripos($packageName, 'zoom') !== false) {
                $serviceIp = $order->sipInterfaceIpAddresses->first();
                $publicIp = $order->sipInterfacePublicIps->first();
            }

            return [
                'provider' => $provider,
                'package' => $firstItem['item']->description,
                'no_user' => $items->count(),
                'address' => $firstItem['service']->address->unit_street . ', ' .
                    ($firstItem['service']->address->housing_area ? $firstItem['service']->address->housing_area . ', ' : '') .
                    $firstItem['service']->address->postcode . ', ' .
                    $firstItem['service']->address->city . ', ' .
                    $firstItem['service']->address->state,
                'phone_number' => $items->map(function($item) {
                    return $item['service']->phoneNumber->phone_number;
                })->values()->toArray(),
                'other_parameters' => [
                    'fqdn' => $fqdn ? $fqdn->hostname . '.' . $fqdn->subdomain : null,
                    'service_ip' => $serviceIp ? $serviceIp->ip_address : null,
                    'public_ip' => $publicIp ? $publicIp->ip_address : null,
                    'package_type' => $packageName,
                    'fqdn_package_type' => $fqdnPackageType,
                    'package_description' => $packageDescription
                ]
            ];
        })->values()->toArray();


        return view('ott-portal.order.view', compact('order', 'lov', 'datatable'));
    }

    /**
     * Debug method to investigate Other Parameters data
     * Temporary method for debugging the "N/A" issue
     */
    public function debugOtherParameters(string $id)
    {
        $order = Order::with(['fqdns', 'sipInterfaceIpAddresses', 'sipInterfacePublicIps', 'latestServices', 'latestServices.serviceItems'])->findOrFail($id);

        $debug = [
            'order_id' => $order->order_id,
            'order_internal_id' => $order->id,
            'fqdns_count' => $order->fqdns->count(),
            'service_ips_count' => $order->sipInterfaceIpAddresses->count(),
            'public_ips_count' => $order->sipInterfacePublicIps->count(),
            'fqdns' => $order->fqdns->toArray(),
            'service_ips' => $order->sipInterfaceIpAddresses->toArray(),
            'public_ips' => $order->sipInterfacePublicIps->toArray(),
            'services' => [],
            'package_analysis' => []
        ];

        // Analyze services and packages
        foreach ($order->latestServices as $service) {
            $serviceData = [
                'service_id' => $service->service_id,
                'service_items' => []
            ];

            foreach ($service->serviceItems as $item) {
                $serviceData['service_items'][] = [
                    'description' => $item->description,
                    'provider' => $item->provider,
                    'serviceable_type' => $item->serviceable_type
                ];

                // Analyze package names
                if ($item->serviceable_type === 'App\Models\Package') {
                    $packageName = strtolower($item->description);
                    $debug['package_analysis'][] = [
                        'original_description' => $item->description,
                        'lowercase_name' => $packageName,
                        'contains_teams' => stripos($packageName, 'teams') !== false,
                        'contains_webex' => stripos($packageName, 'webex') !== false,
                        'contains_zoom' => stripos($packageName, 'zoom') !== false,
                        'expected_fqdn_package' => $this->mapPackageNameToFqdnPackage($packageName)
                    ];
                }
            }

            $debug['services'][] = $serviceData;
        }

        return response()->json($debug, 200, [], JSON_PRETTY_PRINT);
    }

    /**
     * Helper method to map package description to FQDN package enum
     */
    private function mapPackageNameToFqdnPackage(string $packageDescription): ?string
    {
        $packageName = strtolower($packageDescription);

        if (stripos($packageName, 'teams') !== false) {
            return 'teams';
        } elseif (stripos($packageName, 'webex') !== false) {
            return 'webex';
        } elseif (stripos($packageName, 'zoom') !== false) {
            return 'zoom';
        }

        return null;
    }

    /**
     * Populate missing parameter data for existing orders
     * This method helps fix the "N/A" issue by creating missing FQDN records
     */
    public function populateMissingParameters(string $id)
    {
        $order = Order::with(['fqdns', 'sipInterfaceIpAddresses', 'sipInterfacePublicIps', 'latestServices', 'latestServices.serviceItems'])->findOrFail($id);

        $results = [
            'order_id' => $order->order_id,
            'actions_taken' => [],
            'existing_data' => [
                'fqdns' => $order->fqdns->count(),
                'service_ips' => $order->sipInterfaceIpAddresses->count(),
                'public_ips' => $order->sipInterfacePublicIps->count()
            ]
        ];

        // Get unique packages for this order
        $packages = $order->latestServices->flatMap(function($service) {
            return $service->serviceItems->where('serviceable_type', 'App\Models\Package');
        })->unique('description');

        foreach ($packages as $packageItem) {
            $packageDescription = $packageItem->description;
            $fqdnPackageType = $this->mapPackageNameToFqdnPackage($packageDescription);

            if ($fqdnPackageType) {
                // Check if FQDN already exists for this package
                $existingFqdn = $order->fqdns->where('package', $fqdnPackageType)->first();

                if (!$existingFqdn) {
                    // Look for available FQDN records that are not assigned to any order
                    $availableFqdn = \App\Models\Fqdn::where('package', $fqdnPackageType)
                        ->where('status', 'available')
                        ->whereNull('order_id')
                        ->first();

                    if ($availableFqdn) {
                        // Assign the FQDN to this order
                        $availableFqdn->update([
                            'order_id' => $order->id,
                            'customer_id' => $order->customer_id,
                            'status' => 'assigned'
                        ]);

                        $results['actions_taken'][] = [
                            'action' => 'assigned_fqdn',
                            'package' => $fqdnPackageType,
                            'fqdn' => $availableFqdn->hostname . '.' . $availableFqdn->subdomain,
                            'fqdn_id' => $availableFqdn->id
                        ];
                    } else {
                        $results['actions_taken'][] = [
                            'action' => 'no_available_fqdn',
                            'package' => $fqdnPackageType,
                            'message' => 'No available FQDN found for this package'
                        ];
                    }
                } else {
                    $results['actions_taken'][] = [
                        'action' => 'fqdn_already_exists',
                        'package' => $fqdnPackageType,
                        'fqdn' => $existingFqdn->hostname . '.' . $existingFqdn->subdomain
                    ];
                }

                // For Zoom packages, also handle Service IP and Public IP
                if ($fqdnPackageType === 'zoom') {
                    // Check Service IP
                    if ($order->sipInterfaceIpAddresses->count() === 0) {
                        $availableServiceIp = \App\Models\SipInterfaceIpAddress::where('status', 'available')
                            ->whereNull('order_id')
                            ->first();

                        if ($availableServiceIp) {
                            $availableServiceIp->update([
                                'order_id' => $order->id,
                                'customer_id' => $order->customer_id,
                                'status' => 'assigned'
                            ]);

                            $results['actions_taken'][] = [
                                'action' => 'assigned_service_ip',
                                'ip_address' => $availableServiceIp->ip_address,
                                'ip_id' => $availableServiceIp->id
                            ];
                        }
                    }

                    // Check Public IP
                    if ($order->sipInterfacePublicIps->count() === 0) {
                        $availablePublicIp = \App\Models\SipInterfacePublicIp::where('status', 'available')
                            ->whereNull('order_id')
                            ->first();

                        if ($availablePublicIp) {
                            $availablePublicIp->update([
                                'order_id' => $order->id,
                                'customer_id' => $order->customer_id,
                                'status' => 'assigned'
                            ]);

                            $results['actions_taken'][] = [
                                'action' => 'assigned_public_ip',
                                'ip_address' => $availablePublicIp->ip_address,
                                'ip_id' => $availablePublicIp->id
                            ];
                        }
                    }
                }
            }
        }

        return response()->json($results, 200, [], JSON_PRETTY_PRINT);
    }

    public function editUploadDocument(string $id)
    {
        $order = Order::with(['quote', 'quote.quote_items', 'sof', 'updater', 'creator', 'customer', 'customer.addresses', 'customer.addresses.state', 'latestServices', 'latestServices.serviceItems', 'latestServices.address', 'latestServices.address.state', 'fqdns', 'sipInterfaceIpAddresses', 'sipInterfacePublicIps'])->findOrFail($id);

        // Get List of Value from file orderLovs.json   
        $lov = json_decode(Storage::get('app/public/data/orderLov.json'), true);

        // Group services by address
        $datatable = $order->latestServices->groupBy('address_id')->map(function($services) use ($order) {
            $firstService = $services->first();
            $packageDescription = $firstService->serviceItems->first()->description;
            $packageName = strtolower($packageDescription);

            // Map package description to FQDN package enum
            $fqdnPackageType = $this->mapPackageNameToFqdnPackage($packageDescription);

            // Get FQDN for this package
            $fqdn = null;
            if ($fqdnPackageType) {
                $fqdn = $order->fqdns->where('package', $fqdnPackageType)->first();
            }

            // Get Service IP and Public IP for Zoom packages only
            $serviceIp = null;
            $publicIp = null;
            if (stripos($packageName, 'zoom') !== false) {
                $serviceIp = $order->sipInterfaceIpAddresses->first();
                $publicIp = $order->sipInterfacePublicIps->first();
            }

            return [
            'address' => $firstService->address->unit_street . ', ' .
                   ($firstService->address->housing_area ? $firstService->address->housing_area . ', ' : '') .
                   $firstService->address->postcode . ', ' .
                   $firstService->address->city . ', ' .
                   $firstService->address->state,
            'no_user' => $services->count(),
            'package' => $firstService->serviceItems->first()->description,
            // 'phone_number' => implode("\n", $services->pluck('service_id')->toArray()),
            'phone_number' => $services->map(function($service) {
                            return $service->phoneNumber->phone_number;
                        })->toArray(),
            'other_parameters' => [
                'fqdn' => $fqdn ? $fqdn->hostname . '.' . $fqdn->subdomain : null,
                'service_ip' => $serviceIp ? $serviceIp->ip_address : null,
                'public_ip' => $publicIp ? $publicIp->ip_address : null,
                'package_type' => $packageName,
                'fqdn_package_type' => $fqdnPackageType,
                'package_description' => $packageDescription
            ]            ];
        })->values()->toArray();

        return view('ott-portal.order.upload-document', compact('order', 'lov', 'datatable'));
    }

    public function updateUploadDocument(Request $request, Order $order)
    {
        // Create 2 different code flow
        // 1. Upload UAT & COA documents
        if ($order->status == 'service activated') {

            $rules = [
                'am' => 'required|array|min:1',
                'coa_document' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png,xls,xlsx|max:2048',
                'uat_document' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png,xls,xlsx|max:2048',
            ];

            try {

                DB::beginTransaction();

                // Validate the request
                $validator = Validator::make($request->all(), $rules);

                if ($validator->fails()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Validation errors',
                        'errors' => $validator->errors(),
                    ], 422);
                }

                // Modify AM array
                foreach ($request->am as $staff) {
                    // Extract name and email using regex or string splitting
                    if (preg_match('/^(.*) \((.*)\)$/', $staff, $matches)) {
                        $user = User::whereRaw('LOWER(email) = ?', [trim(strtolower($matches[2]))])->first();
                        $amMembers[] = [
                            'user_id' => $user ? $user->id : null,
                            'name' => trim($matches[1]),
                            'email' => trim($matches[2]),
                            'role' => 'AM',
                        ];
                    }
                }

                // Upload COA document
                $coa_document = $request->file('coa_document');
                $coa_document_name = $coa_document->getClientOriginalName();
                $coa_document_path = $coa_document->storeAs('public/documents', $coa_document_name);
                // Save in attachments table
                $attachment = Attachment::create([
                    'attachable_id' => $order->id,
                    'attachable_type' => 'App\Models\Order',
                    'filename' => $coa_document_name,
                    'description' => "COA Document",
                    'mime_type' => $coa_document->getClientMimeType(),
                    'path' => $coa_document_path,
                    'size' => $coa_document->getSize()
                ]);

                // Log Activity - Upload COA document
                AuditService::logActivity(
                    'uploaded',
                    'App\Models\Attachment',
                    $attachment->id,
                    'Uploaded COA document for order: ' . $order->order_id
                );

                // Upload UAT document
                $uat_document = $request->file('uat_document');
                $uat_document_name = $uat_document->getClientOriginalName();
                $uat_document_path = $uat_document->storeAs('public/documents', $uat_document_name);
                // Save in attachments table
                $attachment = Attachment::create([
                    'attachable_id' => $order->id,
                    'attachable_type' => 'App\Models\Order',
                    'filename' => $uat_document_name,
                    'description' => "UAT Document",
                    'mime_type' => $uat_document->getClientMimeType(),
                    'path' => $uat_document_path,
                    'size' => $uat_document->getSize()
                ]);

                // Log Activity - Upload UAT document
                AuditService::logActivity(
                    'uploaded',
                    'App\Models\Attachment',
                    $attachment->id,
                    'Uploaded UAT document for order: ' . $order->order_id
                );

                // Update order with AM
                // Filter out existing TAD members and merge with new ones
                $currentTeam = array_filter(json_decode($order->project_team, true) ?? [], function($member) {
                    return $member['role'] !== 'AM';
                });
                $order->update([
                    'status' => 'service tested',
                    'updated_by' => auth()->id(),
                    'project_team' => json_encode(array_merge($currentTeam, $amMembers)),
                ]);

                // Log Activity - Update order
                AuditService::logActivity(
                    'updated',
                    'App\Models\Order',
                    $order->id,
                    'Updated order ' . $order->order_id. ' with COA and UAT documents'
                );

                // Update activity - Upload UAT & COA
                $activity = Activity::where('trackable_id', $order->id)
                    ->where('trackable_type', get_class($order))
                    ->whereHas('activityType', function ($query) {
                        $query->where('name', 'Upload UAT & COA');
                    })->first();

                $activity->status = 'Done';
                $activity->actual_end_date = now();
                $activity->actual_duration = $activity->actual_end_date->diffInDaysFiltered(function (Carbon $date) {
                    return !$date->isWeekend();
                }, $activity->actual_start_date);
                $activity->is_overdue = ($activity->actual_duration - $activity->activityType->duration > 0) ? true : false;
                $activity->user_id = auth()->user()->id;
                $activity->save();

                // Create activity logs
                AuditService::logActivity(
                    'updated',
                    get_class($activity),
                    $activity->id,
                    'Activity '. $activity->activityType->name . ' status is changed to Done'
                );

                // Create a new activity - Upload PO & Acceptance Form
                $activityType = ActivityType::where('name', 'Upload PO & Acceptance Form')->first();
                // Calculate planned_end_date considering only working days
                $plannedEndDate = Carbon::now();
                $daysToAdd = $activityType->duration;
                while ($plannedEndDate->isWeekend()) {
                    $plannedEndDate->addDay();
                    if (!$plannedEndDate->isWeekend()) {
                        $daysToAdd--;
                    }
                }   

                $activity = Activity::create([
                    'activity_id' =>  (string) Activity::generateUniqueActivityId(),
                    'activity_type_id' => $activityType->id,
                    'trackable_id' => $order->id,
                    'trackable_type' => get_class($order),
                    'status' => 'in progress',
                    'planned_start_date' => now(),
                    'planned_end_date' => $plannedEndDate,
                    'actual_start_date' => now(),
                    'actual_end_date' => null,
                    'actual_duration' => null,
                    'aging' => null,
                    'is_overdue' => false,
                    'user_id' => null
                ]);

                // Create activity logs
                AuditService::logActivity(
                    'generated',
                    get_class($activity),
                    $activity->id,
                    'Activity ' . $activity->activityType->name . ' is created'
                );

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'Order updated successfully',
                ], 200);

            } catch (\Exception $e) {
                DB::rollBack();
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update order',
                    'error' => $e->getMessage(),
                ], 500);
            }


        }
        else {
            $rules = [
                'am' => 'required|array|min:1',
                'po_document' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png,xls,xlsx|max:2048',
                'acceptance_form_document' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png,xls,xlsx|max:2048',
            ];

            try {

                DB::beginTransaction();

                // Validate the request
                $validator = Validator::make($request->all(), $rules);

                if ($validator->fails()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Validation errors',
                        'errors' => $validator->errors(),
                    ], 422);
                }   

                // Upload PO document
                $po_document = $request->file('po_document');
                $po_document_name = $po_document->getClientOriginalName();
                $po_document_path = $po_document->storeAs('public/documents', $po_document_name);
                // Save in attachments table
                $attachment = Attachment::create([
                    'attachable_id' => $order->id,
                    'attachable_type' => 'App\Models\Order',
                    'filename' => $po_document_name,
                    'description' => "PO Document",
                    'mime_type' => $po_document->getClientMimeType(),
                    'path' => $po_document_path,
                    'size' => $po_document->getSize()
                ]);

                // Log Activity - Upload PO document
                AuditService::logActivity(
                    'uploaded',
                    'App\Models\Attachment',
                    $attachment->id,
                    'Uploaded PO document for order: ' . $order->order_id
                );

                // Upload Acceptance Form document
                $acceptance_form_document = $request->file('acceptance_form_document');
                $acceptance_form_document_name = $acceptance_form_document->getClientOriginalName();    
                $acceptance_form_document_path = $acceptance_form_document->storeAs('public/documents', $acceptance_form_document_name);
                // Save in attachments table
                $attachment = Attachment::create([
                    'attachable_id' => $order->id,
                    'attachable_type' => 'App\Models\Order',
                    'filename' => $acceptance_form_document_name,
                    'description' => "Acceptance Form Document",
                    'mime_type' => $acceptance_form_document->getClientMimeType(),
                    'path' => $acceptance_form_document_path,
                    'size' => $acceptance_form_document->getSize()
                ]);

                // Log Activity - Upload Acceptance Form document
                AuditService::logActivity(
                    'uploaded',
                    'App\Models\Attachment',
                    $attachment->id,
                    'Uploaded Acceptance Form document for order: ' . $order->order_id
                );
                
                // Update order
                $order->update([
                    'status' => 'completed',
                    'updated_by' => auth()->id()
                ]);

                // Log Activity - Update order
                AuditService::logActivity(
                    'updated',
                    'App\Models\Order',
                    $order->id,
                    'Updated order ' . $order->order_id. ' with PO and Acceptance Form documents'
                );

                // Update activity - Upload PO & Acceptance Form
                // Get activity - Upload PO & Acceptance Form
                $activity = Activity::where('trackable_id', $order->id)
                    ->where('trackable_type', get_class($order))
                    ->whereHas('activityType', function ($query) {
                        $query->where('name', 'Upload PO & Acceptance Form');
                    })->first();
                    
                $activity->status = 'Done';
                $activity->actual_end_date = now();
                $activity->actual_duration = $activity->actual_end_date->diffInDaysFiltered(function (Carbon $date) {
                    return !$date->isWeekend();
                }, $activity->actual_start_date);
                $activity->is_overdue = ($activity->actual_duration - $activity->activityType->duration > 0) ? true : false;
                $activity->user_id = auth()->user()->id;
                $activity->save();

                // Create activity logs
                AuditService::logActivity(
                    'updated',
                    get_class($activity),
                    $activity->id,
                    'Activity '. $activity->activityType->name . ' status is changed to Done'
                );

                // Create a new contract using model contract
                $contract = Contract::create([
                    // 'contract_id' => (string) Contract::generateUniqueContractId(),
                    'quote_id' => $order->quote->id,
                    'customer_id' => $order->customer->id,
                    'order_id' => $order->id,
                    'contract_duration' => $order->quote->contract_length,
                    'billing_cycle' => null,
                    'start_date' => now(),
                    'end_date' => now()->addMonths($order->quote->contract_length),
                    'renewal_date' => $order->quote->contract_length > 6 ? now()->addMonths($order->quote->contract_length - 3) : now()->addMonths($order->quote->contract_length - 1),
                    'status' => 'active',
                    // 'created_by' => auth()->id(),
                    // 'updated_by' => auth()->id(),
                ]);

                // Log Activity - Create contract
                AuditService::logActivity(
                    'created',
                    'App\Models\Contract',
                    $contract->id,
                    'Created contract: ' . $contract->contract_id . ' for order: ' . $order->order_id
                );

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'Order updated successfully',
                ], 200);
            } catch (\Exception $e) {
                DB::rollBack();
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update order',
                    'error' => $e->getMessage(),
                ], 500);
            }
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  string  $order
     * @return \Illuminate\Http\Response
     */
    public function cancelOrder(string $order)
    {
        // Get order
        $order = Order::with(['quote', 'quote.quote_items', 'sof', 'updater', 'creator', 'customer', 'customer.addresses', 'customer.addresses.state', 'latestServices', 'latestServices.serviceItems', 'latestServices.address', 'latestServices.address.state', 'activities', 'activities.activityType', 'fqdns', 'sipInterfaceIpAddresses', 'sipInterfacePublicIps'])->findOrFail($order);

        // Get List of Value from file orderLovs.json   
        $lov = json_decode(Storage::get('app/public/data/orderLov.json'), true);

        // Group services by address
        // Group services by package and provider
        $datatable = $order->latestServices->flatMap(function($service) {
            // Get only package service items
            return $service->serviceItems->where('serviceable_type', 'App\Models\Package')->map(function($item) use ($service) {
                return [
                    'item' => $item,
                    'service' => $service
                ];
            });
        })->groupBy('item.provider')->map(function($items, $provider) use ($order) {
            $firstItem = $items->first();
            $packageDescription = $firstItem['item']->description;
            $packageName = strtolower($packageDescription);

            // Map package description to FQDN package enum
            $fqdnPackageType = $this->mapPackageNameToFqdnPackage($packageDescription);

            // Get FQDN for this package
            $fqdn = null;
            if ($fqdnPackageType) {
                $fqdn = $order->fqdns->where('package', $fqdnPackageType)->first();
            }

            // Get Service IP and Public IP for Zoom packages only
            $serviceIp = null;
            $publicIp = null;
            if (stripos($packageName, 'zoom') !== false) {
                $serviceIp = $order->sipInterfaceIpAddresses->first();
                $publicIp = $order->sipInterfacePublicIps->first();
            }

            return [
                'provider' => $provider,
                'package' => $firstItem['item']->description,
                'no_user' => $items->count(),
                'address' => $firstItem['service']->address->unit_street . ', ' .
                    ($firstItem['service']->address->housing_area ? $firstItem['service']->address->housing_area . ', ' : '') .
                    $firstItem['service']->address->postcode . ', ' .
                    $firstItem['service']->address->city . ', ' .
                    $firstItem['service']->address->state,
                'phone_number' => $items->map(function($item) {
                    return $item['service']->phoneNumber->phone_number;
                })->values()->toArray(),
                'other_parameters' => [
                    'fqdn' => $fqdn ? $fqdn->hostname . '.' . $fqdn->subdomain : null,
                    'service_ip' => $serviceIp ? $serviceIp->ip_address : null,
                    'public_ip' => $publicIp ? $publicIp->ip_address : null,
                    'package_type' => $packageName,
                    'fqdn_package_type' => $fqdnPackageType,
                    'package_description' => $packageDescription
                ]
            ];
        })->values()->toArray();

        return view('ott-portal.order.cancel', compact('order', 'lov', 'datatable'));

    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $order
     * @return \Illuminate\Http\Response
     */
    public function updateCancelOrder(Request $request, string $id)
    {

        // dd($request->all());

        try {

            // Get the order
            $order = Order::with(['customer'])->findOrFail($id);

            // Add debugging to log request structure
            Log::info('updateCancelOrder request data', [
                'order_id' => $order->order_id,
                'request_keys' => array_keys($request->all()),
                'assigned_detail_structure' => $request->has('assigned_detail') ?
                    collect($request->assigned_detail)->map(function($detail, $index) {
                        return [
                            'index' => $index,
                            'keys' => is_array($detail) ? array_keys($detail) : 'not_array',
                            'type' => gettype($detail)
                        ];
                    })->toArray() : 'not_present'
            ]);

            $rules = [
                'cancel_reason' => 'required',
                'assigned_detail' => 'required|array', // Ensure it's a valid JSON string
                'assigned_detail.*.phone_number' => 'required|array',
                'assigned_detail.*.package' => 'required',
                'assigned_detail.*.no_user' => 'required',
            ];

            $messages = [
                'assigned_detail.*.phone_number.required' => 'Phone number is incomplete',
                'assigned_detail.*.package.required' => 'Package is incomplete',
                'assigned_detail.*.no_user.required' => 'No. of user is incomplete',
            ];

            DB::beginTransaction();

            // Validate request
            $validator = Validator::make($request->all(), $rules, $messages);
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            // Get all services tagged to this order
            $services = $order->latestServices;

            // To determine if SBC and order deactivation are required
            // Updated condition to handle cancelled orders that need SBC termination
            if ($order->status == 'in progress' || strpos($order->status, 'cancelled') !== false) {
                $sbc_activation = false;
                $ott_license_activation = false;
                // Check if any of the services status = 'activated' or 'in service', if yes, set $sbc_activation = true
                // and if 'license_status' = 'activated', set $ott_license_activation = true
                foreach ($services as $service) {
                    // Updated to include 'in service' status for services that completed full activation
                    if (in_array($service->status, ['activated', 'in service'])) {
                        $sbc_activation = true;
                    }
                    if ($service->license_status == 'activated') {
                        $ott_license_activation = true;
                    }
                }

                // Add debug logging for SBC termination trigger analysis
                Log::info('SBC Termination Trigger Analysis', [
                    'order_id' => $order->order_id,
                    'order_status' => $order->status,
                    'sbc_activation' => $sbc_activation,
                    'ott_license_activation' => $ott_license_activation,
                    'service_count' => $services->count(),
                    'services_activated' => $services->where('status', 'activated')->count(),
                    'services_in_service' => $services->where('status', 'in service')->count(),
                    'services_with_activated_license' => $services->where('license_status', 'activated')->count()
                ]);

                if ($sbc_activation) {
                    // Analyze packages to determine processing strategy
                    $packageAnalysis = $this->analyzePackagesForTermination($request->assigned_detail);

                    if ($packageAnalysis['has_standard']) {
                        // Queue automated termination job for standard packages
                        $terminationData = $this->prepareSbcTerminationData($order, $packageAnalysis['standard']);

                        try {
                            $terminationJob = app(SbcTerminationQueueService::class)
                                ->queueTerminationJob($order->order_id, $terminationData, auth()->id());

                            AuditService::logActivity(
                                'generated',
                                'App\Models\SbcTerminationJob',
                                $terminationJob->id,
                                'SBC Termination Job Queued for order ' . $order->order_id . ' with ' . count($packageAnalysis['standard']) . ' standard packages'
                            );
                        } catch (\Exception $e) {
                            // If job queueing fails, create manual activity as fallback
                            $this->createStandardExceptionActivity($order, $packageAnalysis['standard'], null);

                            AuditService::logActivity(
                                'generated',
                                'App\Models\Activity',
                                null,
                                'SBC Termination Job Queue Failed for order ' . $order->order_id . ' - Created Manual Activity. Error: ' . $e->getMessage()
                            );
                        }
                    }

                    if ($packageAnalysis['has_others']) {
                        // Create manual activity for non-standard packages
                        $this->createOthersExceptionActivity($order, $packageAnalysis['others']);
                    }
                }

                if ($ott_license_activation) {
                    // Create a new activity
                    $activityType = ActivityType::where('name', 'Deconfigure OTT License')->first();

                    // Calculate planned_end_date considering only working days
                    $plannedEndDate = Carbon::now();
                    $daysToAdd = $activityType->duration;
                    while ($daysToAdd > 0) {
                        $plannedEndDate->addDay();
                        if (!$plannedEndDate->isWeekend()) {
                            $daysToAdd--;
                        }
                    }

                    $activity = Activity::create([
                        'activity_id' =>  (string) Activity::generateUniqueActivityId(),
                        'activity_type_id' => $activityType->id,
                        'trackable_id' => $order->id,
                        'trackable_type' => get_class($order),
                        'status' => 'in progress',
                        'planned_start_date' => now(),
                        'planned_end_date' => $plannedEndDate,
                        'actual_start_date' => now(),
                        'actual_end_date' => null,
                        'actual_duration' => null,
                        'aging' => null,
                        'is_overdue' => false,
                        'user_id' => null,
                        'notes' => json_encode(collect($request->assigned_detail)
                            ->groupBy('package')
                            ->map(function($group, $package) use ($order) {
                                // Get the provider from quote items
                                $quote_item = $order->quote->quote_items()
                                    ->where('description', $package)
                                    ->first();
                                
                                return [
                                    'ingress_realm' => stripos($package, 'teams') !== false ? "teams-".$order->customer->brn : 
                                                (stripos($package, 'zoom') !== false ? "zoom-".$order->customer->brn : 
                                                (stripos($package, 'webex') !== false ? "webex-".$order->customer->brn : 
                                                (stripos($package, 'other') !== false ? strtolower($quote_item->provider)."-".$order->customer->brn : null))),
                                    'egress_realm' => "tmims-".$order->customer->brn,
                                    'package' => $package,
                                    'phone_number' => $group->flatMap(function($detail) {
                                        return $detail['phone_number'];
                                    })->values()->toArray()
                                ];
                            })->values()->toArray()),
                        'updated_at' => now()->addMinutes(1)
                    ]);

                    // Log Activity - Create activity
                    AuditService::logActivity(
                        'generated',
                        'App\Models\Activity',
                        $activity->id,
                        'Activity ' . $activity->activityType->name . ' is created'
                    );
                }

                // Cancel activity - Exception - Activate SBC (Others) and Exception - Activate SBC (Standard)
                $activityType = ActivityType::where('name', 'Exception - Activate SBC (Others)')->first();
                $activity = Activity::where('trackable_id', $order->id)
                    ->where('trackable_type', get_class($order))
                    ->where('activity_type_id', $activityType->id)
                    ->first();

                // Check if activity exists
                if ($activity) {

                    // Check activity status
                    if ($activity->status == 'in progress') {

                        $activity->status = 'Cancelled';
                        $activity->actual_end_date = now();
                        $activity->actual_duration = $activity->actual_end_date->diffInDaysFiltered(function (Carbon $date) {
                            return !$date->isWeekend();
                        }, $activity->actual_start_date);
                        $activity->is_overdue = ($activity->actual_duration - $activity->activityType->duration > 0) ? true : false;
                        $activity->user_id = auth()->user()->id;
                        $activity->save();

                        // Log activity
                        AuditService::logActivity(
                            'updated',
                            get_class($activity),
                            $activity->id,
                            'Cancelled activity ' . $activity->activityType->name,
                        ); 
                        
                    }  

                }

                $activityType = ActivityType::where('name', 'Exception - Activate SBC (Standard)')->first();
                $activity = Activity::where('trackable_id', $order->id)
                    ->where('trackable_type', get_class($order))
                    ->where('activity_type_id', $activityType->id)
                    ->first();

                // Check if activity exists
                if ($activity) {

                    // Check activity status
                    if ($activity->status == 'in progress') {

                        $activity->status = 'Cancelled';
                        $activity->actual_end_date = now();
                        $activity->actual_duration = $activity->actual_end_date->diffInDaysFiltered(function (Carbon $date) {
                            return !$date->isWeekend();
                        }, $activity->actual_start_date);
                        $activity->is_overdue = ($activity->actual_duration - $activity->activityType->duration > 0) ? true : false;
                        $activity->user_id = auth()->user()->id;
                        $activity->save();

                        // Log activity
                        AuditService::logActivity(
                            'updated',
                            get_class($activity),
                            $activity->id,
                            'Cancelled activity ' . $activity->activityType->name,
                        ); 
                        
                    }  

                }

                // Cancel activity - Configure OTT License
                $activityType = ActivityType::where('name', 'Configure OTT License')->first();
                $activity = Activity::where('trackable_id', $order->id)
                    ->where('trackable_type', get_class($order))
                    ->where('activity_type_id', $activityType->id)
                    ->first();

                // Check activity status
                if ($activity->status == 'in progress') {

                    $activity->status = 'Cancelled';
                    $activity->actual_end_date = now();
                    $activity->actual_duration = $activity->actual_end_date->diffInDaysFiltered(function (Carbon $date) {
                        return !$date->isWeekend();
                    }, $activity->actual_start_date);
                    $activity->is_overdue = ($activity->actual_duration - $activity->activityType->duration > 0) ? true : false;
                    $activity->user_id = auth()->user()->id;
                    $activity->save();

                    // Log activity
                    AuditService::logActivity(
                        'updated',
                        get_class($activity),
                        $activity->id,
                        'Cancelled activity ' . $activity->activityType->name,
                    );  
                    
                } 

                // Cancel activity - Configure FQDN
                $activityType = ActivityType::where('name', 'Configure FQDN')->first();
                $activity = Activity::where('trackable_id', $order->id)
                    ->where('trackable_type', get_class($order))
                    ->where('activity_type_id', $activityType->id)
                    ->first();

                // Check if activity exists
                if ($activity) {

                    // Check activity status
                    if ($activity->status == 'in progress') {

                        $activity->status = 'Cancelled';
                        $activity->actual_end_date = now();
                        $activity->actual_duration = $activity->actual_end_date->diffInDaysFiltered(function (Carbon $date) {
                            return !$date->isWeekend();
                        }, $activity->actual_start_date);
                        $activity->is_overdue = ($activity->actual_duration - $activity->activityType->duration > 0) ? true : false;
                        $activity->user_id = auth()->user()->id;
                        $activity->save();

                        // Log activity
                        AuditService::logActivity(
                            'updated',
                            get_class($activity),
                            $activity->id,
                            'Cancelled activity ' . $activity->activityType->name,
                        ); 
                        
                    }  

                }

            }
            else if ($order->status == 'service activated') {

                // Analyze packages to determine processing strategy
                $packageAnalysis = $this->analyzePackagesForTermination($request->assigned_detail);

                if ($packageAnalysis['has_standard']) {
                    // Queue automated termination job for standard packages
                    $terminationData = $this->prepareSbcTerminationData($order, $packageAnalysis['standard']);

                    try {
                        $terminationJob = app(SbcTerminationQueueService::class)
                            ->queueTerminationJob($order->order_id, $terminationData, auth()->id());

                        AuditService::logActivity(
                            'generated',
                            'App\Models\SbcTerminationJob',
                            $terminationJob->id,
                            'SBC Termination Job Queued for order ' . $order->order_id . ' with ' . count($packageAnalysis['standard']) . ' standard packages'
                        );
                    } catch (\Exception $e) {
                        // If job queueing fails, create manual activity as fallback
                        $this->createStandardExceptionActivity($order, $packageAnalysis['standard'], null);

                        AuditService::logActivity(
                            'generated',
                            'App\Models\Activity',
                            null,
                            'SBC Termination Job Queue Failed for order ' . $order->order_id . ' - Created Manual Activity. Error: ' . $e->getMessage()
                        );
                    }
                }

                if ($packageAnalysis['has_others']) {
                    // Create manual activity for non-standard packages
                    $this->createOthersExceptionActivity($order, $packageAnalysis['others']);
                }

                // Create a new activity - Deconfigure OTT License
                $activityType = ActivityType::where('name', 'Deconfigure OTT License')->first();

                // Calculate planned_end_date considering only working days
                $plannedEndDate = Carbon::now();
                $daysToAdd = $activityType->duration;
                while ($daysToAdd > 0) {
                    $plannedEndDate->addDay();
                    if (!$plannedEndDate->isWeekend()) {
                        $daysToAdd--;
                    }
                }

                $activity = Activity::create([
                    'activity_id' =>  (string) Activity::generateUniqueActivityId(),
                    'activity_type_id' => $activityType->id,
                    'trackable_id' => $order->id,
                    'trackable_type' => get_class($order),
                    'status' => 'in progress',
                    'planned_start_date' => now(),
                    'planned_end_date' => $plannedEndDate,
                    'actual_start_date' => now(),
                    'actual_end_date' => null,
                    'actual_duration' => null,
                    'aging' => null,
                    'is_overdue' => false,
                    'user_id' => null,
                    'notes' => json_encode(collect($request->assigned_detail)
                        ->groupBy('package')
                            ->map(function($group, $package) use ($order) {
                                // Get the provider from quote items
                                $quote_item = $order->quote->quote_items()
                                    ->where('description', $package)
                                    ->first();

                                return [
                                    'ingress_realm' => stripos($package, 'teams') !== false ? "teams-".$this->sanitizeBrn($order->customer->brn) :
                                                (stripos($package, 'zoom') !== false ? "zoom-".$this->sanitizeBrn($order->customer->brn) :
                                                (stripos($package, 'webex') !== false ? "webex-".$this->sanitizeBrn($order->customer->brn) :
                                                (stripos($package, 'other') !== false ? strtolower($quote_item->provider ?? 'unknown')."-".$this->sanitizeBrn($order->customer->brn) : 'unknown-'.$this->sanitizeBrn($order->customer->brn)))),
                                    'egress_realm' => "tmims-".$this->sanitizeBrn($order->customer->brn),
                                    'package' => $package,
                                    'phone_number' => $group->flatMap(function($detail) {
                                        // Add defensive programming to handle missing keys
                                        if (!is_array($detail)) {
                                            Log::warning('updateCancelOrder notes generation: detail is not an array', ['detail' => $detail]);
                                            return [];
                                        }

                                        if (!isset($detail['phone_number'])) {
                                            Log::warning('updateCancelOrder notes generation: phone_number key missing', ['detail_keys' => array_keys($detail)]);
                                            return [];
                                        }

                                        return is_array($detail['phone_number']) ? $detail['phone_number'] : [$detail['phone_number']];
                                    })->values()->toArray()
                                ];
                            })->values()->toArray()),
                ]);

                // Log Activity - Create activity
                AuditService::logActivity(
                    'generated',
                    'App\Models\Activity',
                    $activity->id,
                    'Activity ' . $activity->activityType->name . ' is created'
                );

                // Cancel activity - Upload UAT & COA
                $activityType = ActivityType::where('name', 'Upload UAT & COA')->first();
                $activity = Activity::where('trackable_id', $order->id)
                    ->where('trackable_type', get_class($order))
                    ->where('activity_type_id', $activityType->id)
                    ->first();

                $activity->status = 'Cancelled';
                $activity->actual_end_date = now();
                $activity->actual_duration = $activity->actual_end_date->diffInDaysFiltered(function (Carbon $date) {
                    return !$date->isWeekend();
                }, $activity->actual_start_date);
                $activity->is_overdue = ($activity->actual_duration - $activity->activityType->duration > 0) ? true : false;
                $activity->user_id = auth()->user()->id;
                $activity->save();

                // Log activity
                AuditService::logActivity(
                    'updated',
                    get_class($activity),
                    $activity->id,
                    'Updated activity ' . $activity->activityType->name,
                );

            }

            // Check current order status
            if ($order->status == 'service activated') $order_status = 'cancelled (service activated)';
            else if ($order->status == 'in progress') $order_status = 'cancelled (in progress)';

            $order->update([
                'status' => $order_status,
                'cancel_reason' => $request->cancel_reason,
                'updated_by' => auth()->id(),
            ]);

            // Log Activity - Update order
            AuditService::logActivity(
                'updated',
                'App\Models\Order',
                $order->id,
                'Cancelled order ' . $order->order_id
            );

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Order cancelled successfully',
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel order',
                'error' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ], 500);
        }
    }

    // Generate Service Id
    public function generateUniqueServiceId($phone_number)
    {
        // Format 'CS-' + 4 random numbers + '-' + phone number
        $serviceId = 'CS-' . str_pad(mt_rand(0, 99), 2, '0', STR_PAD_LEFT) . $phone_number;
        // Check if the generated ID already exists
        $exists = Service::where('service_id', $serviceId)->exists();
        if ($exists) {
            return $this->generateUniqueServiceId($phone_number);
        }
        return $serviceId;
    }

    /**
     * Analyze customer packages to determine SBC provisioning strategy
     */
    private function analyzeCustomerPackages(array $assignedDetail, array $validatedData, Order $order): array
    {
        $standardPackages = [];
        $othersPackages = [];
        $allPackages = [];

        foreach ($assignedDetail as $detail) {
            $packageName = strtolower($detail['package']);
            $packageData = [
                'package' => $detail['package'],
                'phone_number' => $detail['phone_number'],
                'no_user' => $detail['no_user'],
                'ingress_realm' => $this->generateIngressRealm($detail['package'], $validatedData['brn'], $order),
                'egress_realm' => "tmims-" . $validatedData['brn']
            ];

            $allPackages[] = $packageData;

            if (stripos($packageName, 'other') !== false) {
                $othersPackages[] = $packageData;
            } elseif (stripos($packageName, 'teams') !== false ||
                      stripos($packageName, 'webex') !== false ||
                      stripos($packageName, 'zoom') !== false ||
                      stripos($packageName, 'sbcaas') !== false) {
                $standardPackages[] = $packageData;
            } else {
                // Unknown package type, treat as others
                $othersPackages[] = $packageData;
            }
        }

        // Determine strategy
        $strategy = 'unknown';
        if (!empty($othersPackages) && empty($standardPackages)) {
            $strategy = 'others_only';
        } elseif (empty($othersPackages) && !empty($standardPackages)) {
            $strategy = 'standard_only';
        } elseif (!empty($othersPackages) && !empty($standardPackages)) {
            $strategy = 'mixed_packages';
        }

        Log::info('Customer package analysis completed', [
            'strategy' => $strategy,
            'standard' => $standardPackages,
            'others' => $othersPackages,
            'all' => $allPackages,
            'summary' => [
                'total_packages' => count($allPackages),
                'standard_count' => count($standardPackages),
                'others_count' => count($othersPackages)
            ]
        ]);

        return [
            'strategy' => $strategy,
            'standard' => $standardPackages,
            'others' => $othersPackages,
            'all' => $allPackages,
            'summary' => [
                'total_packages' => count($allPackages),
                'standard_count' => count($standardPackages),
                'others_count' => count($othersPackages)
            ]
        ];
    }

    /**
     * Generate ingress realm based on package type
     */
    private function generateIngressRealm(string $package, string $brn, Order $order): string
    {
        $packageLower = strtolower($package);

        if (stripos($packageLower, 'teams') !== false) {
            return "teams-{$brn}";
        } elseif (stripos($packageLower, 'zoom') !== false) {
            return "zoom-{$brn}";
        } elseif (stripos($packageLower, 'webex') !== false) {
            return "webex-{$brn}";
        } elseif (stripos($packageLower, 'other') !== false) {
            // For others, try to get provider from quote items
            $provider = 'unknown';
            if ($order->quote) {
                $quoteItem = $order->quote->quote_items()
                    ->where('description', $package)
                    ->first();
                if ($quoteItem) {
                    $provider = $quoteItem->provider ?? 'unknown';
                }
            }
            return strtolower($provider) . "-{$brn}";
        }

        return "unknown-{$brn}";
    }

    /**
     * Queue SBC provisioning for standard packages
     */
    private function queueSbcProvisioning(Order $order, array $standardPackages): array
    {
        try {
            $sbcQueueService = app(SbcProvisioningQueueService::class);
            $sbcJob = $sbcQueueService->queueProvisioningJob($order->order_id);

            // If we get here, the job was created successfully
            Log::info('SBC provisioning queued successfully', [
                'order_id' => $order->order_id,
                'job_uuid' => $sbcJob->job_uuid,
                'package_count' => count($standardPackages)
            ]);

            return [
                'success' => true,
                'message' => 'SBC provisioning initiated for standard packages',
                'job_uuid' => $sbcJob->job_uuid
            ];
        } catch (\Exception $e) {
            Log::error('SBC provisioning queue exception', [
                'order_id' => $order->order_id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'SBC provisioning system error: ' . $e->getMessage(),
                'job_uuid' => null
            ];
        }
    }

    /**
     * Create SBC exception activity with conditional notes content
     */
    private function createSbcExceptionActivity(Order $order, array $packages, string $scenario, array $validatedData): void
    {
        $activityType = ActivityType::where('name', 'Exception - Activate SBC (Others)')->first();

        if (!$activityType) {
            Log::error('Exception - Activate SBC (Others) activity type not found');
            return;
        }

        // Calculate planned_end_date considering only working days
        $plannedEndDate = Carbon::now();
        $daysToAdd = $activityType->duration;
        while ($daysToAdd > 0) {
            $plannedEndDate->addDay();
            if (!$plannedEndDate->isWeekend()) {
                $daysToAdd--;
            }
        }

        // Generate notes based on scenario and packages
        $notes = $this->generateActivityNotes($packages, $scenario, $validatedData, $order);

        $activity = Activity::create([
            'activity_id' => (string) Activity::generateUniqueActivityId(),
            'activity_type_id' => $activityType->id,
            'trackable_id' => $order->id,
            'trackable_type' => get_class($order),
            'status' => 'in progress',
            'planned_start_date' => now(),
            'planned_end_date' => $plannedEndDate,
            'actual_start_date' => now(),
            'actual_end_date' => null,
            'actual_duration' => null,
            'aging' => null,
            'is_overdue' => false,
            'user_id' => null,
            'notes' => json_encode($notes),
        ]);

        // Create activity logs
        AuditService::logActivity(
            'generated',
            get_class($activity),
            $activity->id,
            'Activity ' . $activity->activityType->name . ' is created - Scenario: ' . $scenario
        );

        Log::info('SBC exception activity created', [
            'order_id' => $order->order_id,
            'activity_id' => $activity->id,
            'scenario' => $scenario,
            'package_count' => count($packages)
        ]);
    }

    /**
     * Generate activity notes based on packages and scenario
     */
    private function generateActivityNotes(array $packages, string $scenario, array $validatedData, Order $order): array
    {
        return collect($packages)->map(function($packageData) use ($validatedData, $order, $scenario) {
            return [
                'ingress_realm' => $packageData['ingress_realm'] ?? $this->generateIngressRealm($packageData['package'], $validatedData['brn'], $order),
                'egress_realm' => $packageData['egress_realm'] ?? "tmims-{$validatedData['brn']}",
                'package' => $packageData['package'],
                'phone_number' => $packageData['phone_number'],
                'scenario' => $scenario
            ];
        })->values()->toArray();
    }

    /**
     * Analyze packages to determine termination processing strategy
     *
     * @param array $assignedDetail
     * @return array
     */
    private function analyzePackagesForTermination(array $assignedDetail): array
    {
        $standardPackages = [];
        $othersPackages = [];

        foreach ($assignedDetail as $detail) {
            $packageName = strtolower($detail['package']);

            // Check if package is standard (teams, zoom, webex)
            if (stripos($packageName, 'teams') !== false ||
                stripos($packageName, 'zoom') !== false ||
                stripos($packageName, 'webex') !== false) {
                $standardPackages[] = $detail;
            } else {
                $othersPackages[] = $detail;
            }
        }

        return [
            'standard' => $standardPackages,
            'others' => $othersPackages,
            'has_standard' => !empty($standardPackages),
            'has_others' => !empty($othersPackages)
        ];
    }

    /**
     * Prepare termination data for SBC job
     *
     * @param Order $order
     * @param array $standardPackages
     * @return array
     */
    private function prepareSbcTerminationData(Order $order, array $standardPackages): array
    {
        $terminationData = [
            'order_id' => $order->order_id,
            'customer_name' => $order->customer->name,
            'username' => auth()->user()->name,
            'packages' => []
        ];

        foreach ($standardPackages as $package) {
            $terminationData['packages'][] = [
                'package' => $package['package'],
                'phone_number' => $package['phone_number'],
                'no_user' => $package['no_user'] ?? 0,
                'ingress_realm' => $this->generateIngressRealmForTermination($package['package'], $order->customer->brn),
                'egress_realm' => $this->generateEgressRealmForTermination($order->customer->brn)
            ];
        }

        return $terminationData;
    }

    /**
     * Generate ingress realm for termination
     */
    private function generateIngressRealmForTermination(string $package, string $customerBrn): string
    {
        $packagePrefix = strtolower($package);
        if (stripos($package, 'teams') !== false) {
            $packagePrefix = 'teams';
        } elseif (stripos($package, 'zoom') !== false) {
            $packagePrefix = 'zoom';
        } elseif (stripos($package, 'webex') !== false) {
            $packagePrefix = 'webex';
        }

        return "{$packagePrefix}-{$customerBrn}";
    }

    /**
     * Generate egress realm for termination
     */
    private function generateEgressRealmForTermination(string $customerBrn): string
    {
        return "tmims-{$customerBrn}";
    }

    /**
     * Create Exception - Deactivate SBC (Standard) activity
     *
     * @param Order $order
     * @param array $packages
     * @param string|null $jobUuid
     * @return Activity
     */
    private function createStandardExceptionActivity(Order $order, array $packages, ?string $jobUuid = null): Activity
    {
        $activityType = ActivityType::where('name', 'Exception - Deactivate SBC (Standard)')->first();

        if (!$activityType) {
            throw new \Exception('Activity type "Exception - Deactivate SBC (Standard)" not found');
        }

        $plannedEndDate = $this->calculatePlannedEndDate($activityType->duration);

        $activity = new Activity();
        $activity->activity_id = (string) Activity::generateUniqueActivityId();
        $activity->activity_type_id = $activityType->id;
        $activity->trackable_type = 'App\Models\Order';
        $activity->trackable_id = $order->id;
        $activity->status = 'in progress';
        $activity->planned_start_date = now();
        $activity->planned_end_date = $plannedEndDate;
        $activity->actual_start_date = now();
        $activity->notes = json_encode($this->formatPackageNotesForTermination($packages, $order));
        $activity->user_id = auth()->id();

        if ($jobUuid) {
            $activity->job_reference = $jobUuid;
        }

        $activity->save();

        AuditService::logActivity(
            'generated',
            'App\Models\Activity',
            $activity->id,
            'Activity ' . $activity->activityType->name . ' is created'
        );

        return $activity;
    }

    /**
     * Create Exception - Deactivate SBC (Others) activity
     *
     * @param Order $order
     * @param array $packages
     * @return Activity
     */
    private function createOthersExceptionActivity(Order $order, array $packages): Activity
    {
        $activityType = ActivityType::where('name', 'Exception - Deactivate SBC (Others)')->first();

        if (!$activityType) {
            throw new \Exception('Activity type "Exception - Deactivate SBC (Others)" not found');
        }

        $plannedEndDate = $this->calculatePlannedEndDate($activityType->duration);

        $activity = new Activity();
        $activity->activity_id = (string) Activity::generateUniqueActivityId();
        $activity->activity_type_id = $activityType->id;
        $activity->trackable_type = 'App\Models\Order';
        $activity->trackable_id = $order->id;
        $activity->status = 'in progress';
        $activity->planned_start_date = now();
        $activity->planned_end_date = $plannedEndDate;
        $activity->actual_start_date = now();
        $activity->notes = json_encode($this->formatPackageNotesForTermination($packages, $order));
        $activity->user_id = auth()->id();
        $activity->save();

        AuditService::logActivity(
            'generated',
            'App\Models\Activity',
            $activity->id,
            'Activity ' . $activity->activityType->name . ' is created'
        );

        return $activity;
    }

    /**
     * Calculate planned end date considering working days
     */
    private function calculatePlannedEndDate(int $duration): Carbon
    {
        $plannedEndDate = Carbon::now();
        $daysToAdd = $duration;
        while ($daysToAdd > 0) {
            $plannedEndDate->addDay();
            if (!$plannedEndDate->isWeekend()) {
                $daysToAdd--;
            }
        }
        return $plannedEndDate;
    }

    /**
     * Format package data for termination activity notes
     *
     * @param array $packages
     * @param Order $order
     * @return array
     */
    private function formatPackageNotesForTermination(array $packages, Order $order): array
    {
        return collect($packages)->groupBy('package')->map(function($group, $package) use ($order) {
            // Get the provider from quote items
            $quote_item = $order->quote->quote_items()
                ->where('description', $package)
                ->first();

            return [
                'ingress_realm' => stripos($package, 'teams') !== false ? "teams-".$order->customer->brn :
                            (stripos($package, 'zoom') !== false ? "zoom-".$order->customer->brn :
                            (stripos($package, 'webex') !== false ? "webex-".$order->customer->brn :
                            (stripos($package, 'other') !== false ? strtolower($quote_item->provider ?? 'unknown')."-".$order->customer->brn : 'unknown-'.$order->customer->brn))),
                'egress_realm' => "tmims-".$order->customer->brn,
                'package' => $package,
                'phone_number' => $group->flatMap(function($detail) {
                    // Add defensive programming to handle missing keys
                    if (!is_array($detail)) {
                        Log::warning('formatPackageNotesForTermination: detail is not an array', ['detail' => $detail]);
                        return [];
                    }

                    if (!isset($detail['phone_number'])) {
                        Log::warning('formatPackageNotesForTermination: phone_number key missing', ['detail_keys' => array_keys($detail)]);
                        return [];
                    }

                    return is_array($detail['phone_number']) ? $detail['phone_number'] : [$detail['phone_number']];
                })->values()->toArray()
            ];
        })->values()->toArray();
    }

    /**
     * Sanitize BRN for use in filenames by replacing problematic characters
     * Replaces forward slashes and other filesystem special characters
     *
     * @param string $brn Business Registration Number
     * @return string Sanitized BRN safe for use in filenames
     */
    private function sanitizeBrn(string $brn): string
    {
        // Replace forward slashes and other problematic filesystem characters
        // with hyphens to make it safe for filenames
        return str_replace(['/', '\\', ':', '*', '?', '"', '<', '>', '|'], '-', $brn);
    }

}

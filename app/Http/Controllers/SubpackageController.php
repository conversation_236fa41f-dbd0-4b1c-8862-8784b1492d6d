<?php

namespace App\Http\Controllers;

use App\Models\Price;
use App\Models\Subpackage;
use App\Models\ImeRatePricing;
use App\Services\AuditService;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;


class SubpackageController extends Controller
{
    // format datetime
    public function reformatDateTime($dateTime) {
        // Convert the default timestamp format to the desired format
        return Carbon::parse($dateTime, 'UTC')->setTimezone(config('app.timezone'))->format('d-m-Y H:i');
    }

    /**
     * Display a listing of the resource.
     */
    public function index($subpackageId = null)
    {
        return view('ott-portal.product.subpackage.index', compact('subpackageId'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {

            $currentDate = now();   // Get the current date and time

            if ($id === 'all') {
                $subpackages = Subpackage::with([
                    'package',
                    // 'prices' => function ($query) use ($currentDate) {
                    //     $query->where('effective_from', '<=', $currentDate)
                    //     ->where(function ($query) use ($currentDate) {
                    //         $query->where('effective_to', '>=', $currentDate)
                    //             ->orWhereNull('effective_to');
                    //     });
                    // },
                    'creator',
                    'updater',
                    'package.product',
                    // 'package.vertical',
                    'package.provider',
                    // 'quote_items'
                ])->get()->toArray();
                // return response()->json(compact('subpackages'));
            }
            else {
                $subpackage = Subpackage::with([
                    'package',
                    // 'prices' => function ($query) use ($currentDate) {
                    //     $query->where('effective_from', '<=', $currentDate)
                    //     ->where(function ($query) use ($currentDate) {
                    //         $query->where('effective_to', '>=', $currentDate)
                    //             ->orWhereNull('effective_to');
                    //     });
                    // },
                    'creator',
                    'updater',
                    'package.product',
                    // 'package.vertical',
                    'package.provider',
                    // 'quote_items'
                ])->find($id)->toArray();
                // return response()->json(compact('subpackages'));
            }

            // Add the prices to the subpackage
            if ($id === 'all') {
                foreach ($subpackages as $key => $subpackage) {
                    $subpackages[$key]['prices'] = Price::with(['updater'])->where('entity_type', 'global')
                        ->where('effective_from', '<=', $currentDate)
                        ->where(function ($query) use ($currentDate) {
                            $query->where('effective_to', '>=', $currentDate)
                                ->orWhereNull('effective_to');
                        })
                        ->get();
                }
            } else {
                $subpackage['prices'] = Price::with(['updater'])->where('entity_type', 'global')
                    ->where('effective_from', '<=', $currentDate)
                    ->where(function ($query) use ($currentDate) {
                        $query->where('effective_to', '>=', $currentDate)
                            ->orWhereNull('effective_to');
                    })
                    ->get();
            }

            // Check if packages exist
            if (empty($subpackages) && empty($subpackage)) $data = [];
            else {
                if ($id === 'all') {
                    foreach ($subpackages as $subpackage) {
                        $price = "";
                        foreach ($subpackage['prices'] as $p) {
                            $price .= $p['description'].' = RM'. number_format($p['amount'], 2).'<br>';
                        }
                        
                        // Get the latest updated price to use for last_updated_at and last_updated_by
                        $latestPrice = collect($subpackage['prices'])->sortByDesc('updated_at')->first();
                        $lastUpdatedBy = $latestPrice && isset($latestPrice['updated_by']) && isset($latestPrice['updater']) ? 
                            $latestPrice['updater']['staff_id'] : 
                            ($subpackage['updater']['staff_id'] ?? 'Chyper');
                        $lastUpdatedAt = $latestPrice && isset($latestPrice['updated_at']) ? 
                            $latestPrice['updated_at'] : 
                            $subpackage['updated_at'];
                        
                        $data[] = [
                            'id' => $subpackage['id'],
                            'package' => $subpackage['package']['product']['name'].' - '.$subpackage['package']['provider']['name']. ' (' . ucfirst($subpackage['package']['name']) . ')',
                            'name' => ucwords($subpackage['name']),
                            'material_no' => $subpackage['material_number'],
                            'price' => $price,
                            'status' => "<span class='badge " . 
                                ($subpackage['status'] == 'active' ? 'bg-success-subtle text-success' : 'bg-danger-subtle text-danger') . 
                                " font-size-12'>" . ucwords($subpackage['status']) . "</span>",
                            'created_by' => $subpackage['creator']['staff_id'] ?? 'Chyper',
                            'last_updated_by' => $lastUpdatedBy,
                            'created_at' => $this->reformatDateTime($subpackage['created_at']),
                            'last_updated_at' => $this->reformatDateTime($lastUpdatedAt),
                            'action' => "<a href='#' class='px-1 text-secondary btn-edit' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Edit'><i class='bx bx-edit-alt font-size-20'></i></a>"
                        ];
                    }
                } else {
                    $price = "";
                    foreach ($subpackage['prices'] as $p) {
                        $price .= $p['description'].' = RM'. number_format($p['amount'], 2).'<br>';
                    }
                    
                    // Get the latest updated price to use for last_updated_at and last_updated_by
                    $latestPrice = collect($subpackage['prices'])->sortByDesc('updated_at')->first();
                    $lastUpdatedBy = $latestPrice && isset($latestPrice['updated_by']) && isset($latestPrice['updater']) ? 
                        $latestPrice['updater']['staff_id'] : 
                        ($subpackage['updater']['staff_id'] ?? 'Chyper');
                    $lastUpdatedAt = $latestPrice && isset($latestPrice['updated_at']) ? 
                        $latestPrice['updated_at'] : 
                        $subpackage['updated_at'];
                    
                    $data[] = [
                        'id' => $subpackage['id'],
                        'package' => $subpackage['package']['product']['name'].' - '.$subpackage['package']['provider']['name']. ' (' . ucfirst($subpackage['package']['name']) . ')',
                        'name' => ucwords($subpackage['name']),
                        'material_no' => $subpackage['material_number'],
                        'price' => $price,
                        'status' => "<span class='badge " . 
                            ($subpackage['status'] == 'active' ? 'bg-success-subtle text-success' : 'bg-danger-subtle text-danger') . 
                            " font-size-12'>" . ucwords($subpackage['status']) . "</span>",
                        'created_by' => $subpackage['creator']['staff_id'] ?? 'Chyper',
                        'created_at' => $this->reformatDateTime($subpackage['created_at']),
                        'last_updated_at' => $this->reformatDateTime($lastUpdatedAt),
                        'last_updated_by' => $lastUpdatedBy,
                        'action' => "<a href='#' class='px-1 text-secondary btn-edit' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Edit'><i class='bx bx-edit-alt font-size-20'></i></a>"
                    ];
                }
            }

            // AuditService::logActivity(
            //     'viewed',
            //     'App\Models\Subpackage',
            //     ($id == 'all') ? null : $id,
            //     ($id == 'all') ? 'Viewed all subpackage details': 'Viewed subpackage details'
            // );

            return response()->json(compact('data'));
        } catch (\Exception $e) {
            return [
                'Error: ' . $e->getMessage(),
                'Line: ' . $e->getLine(),
                'File: ' . $e->getFile(),
            ];
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Subpackage $subpackage)
    {
        // Get fixed to fixed and fixed to mobile prices
        $allPrices = Price::where('entity_type', 'global')
            ->where('effective_from', '<=', now())
            ->where(function ($query) {
                $query->where('effective_to', '>=', now())
                    ->orWhereNull('effective_to');
            })
            ->get();

        $prices = [
            'fixed_to_fixed' => $allPrices->where('description', 'fixed to fixed')->first(),
            'fixed_to_mobile' => $allPrices->where('description', 'fixed to mobile')->first()
        ];

        // dd(compact('prices', 'subpackage'));
        return view('ott-portal.product.subpackage.edit', compact('subpackage', 'prices'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Subpackage $subpackage)
    {
        try {

            DB::beginTransaction(); // Start a database transaction

            // Validate the request
            $rules = [
                'material_number' => 'required|string|max:255',
                'fixed_to_fixed_price' => 'required|numeric',
                'fixed_to_mobile_price' => 'required|numeric',
                'fixed_to_fixed_price_id' => 'required|numeric',
                'fixed_to_mobile_price_id' => 'required|numeric',
            ];
            $validatedData = Validator::make($request->all(), $rules);
            if ($validatedData->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Check all required fields and format',
                    'errors' => $validatedData->errors(),
                ], 422);
            }

            // Check if there are any changes
            $changes = [];
            if ($request->input('material_number') !== $subpackage->material_number) {
                $changes['material_number'] = $request->input('material_number');
                // Update the material number
                $subpackage->material_number = $request->input('material_number');
                $subpackage->save();

                // Log the change
                AuditService::logActivity(
                    'updated',
                    'App\Models\Subpackage',
                    $subpackage->id,
                    'Updated material number from ' . $subpackage->getOriginal('material_number') . ' to ' . $request->input('material_number') . ' for subpackage ' . $subpackage->description
                );
            }

            // Check if the prices have changed
            // Get the current price for fixed to fixed and fixed to mobile
            $currentFixedToFixedPrice = Price::where('entity_type', 'global')
                ->where('description', 'fixed to fixed')
                ->where('effective_from', '<=', now())
                ->where(function ($query) {
                    $query->where('effective_to', '>=', now())
                        ->orWhereNull('effective_to');
                })
                ->first();
            $currentFixedToMobilePrice = Price::where('entity_type', 'global')
                ->where('description', 'fixed to mobile')
                ->where('effective_from', '<=', now())
                ->where(function ($query) {
                    $query->where('effective_to', '>=', now())
                        ->orWhereNull('effective_to');
                })
                ->first();
            
            // Check if the fixed to fixed price has changed
            if (number_format($request->input('fixed_to_fixed_price'), 2) !== number_format($currentFixedToFixedPrice->amount, 2)) {

                $changes['fixed_to_fixed_price'] = $request->input('fixed_to_fixed_price');
                // Update the fixed to fixed price
                $currentFixedToFixedPrice->effective_to = now();
                $currentFixedToFixedPrice->save();

                $price = Price::create([
                    'entity_id' => $currentFixedToFixedPrice->entity_id,
                    'entity_type' => 'global',
                    'description' => 'fixed to fixed',
                    'amount' => number_format($request->input('fixed_to_fixed_price'), 2),
                    'effective_from' => now(),
                    'price_type' => 'published_price',
                ]);

                // Log the change
                AuditService::logActivity(
                    'updated',
                    'App\Models\Price',
                    $price->id,
                    'Updated fixed to fixed price from RM' . number_format($price->getOriginal('amount'), 2) . ' to RM' . number_format($request->input('fixed_to_fixed_price'), 2) . ' for subpackage ' . $subpackage->description
                );
                
            }

            // Check if the fixed to mobile price has changed
            if (number_format($request->input('fixed_to_mobile_price'), 2) !== number_format($currentFixedToMobilePrice->amount, 2)) {
                
                $changes['fixed_to_mobile_price'] = $request->input('fixed_to_mobile_price');
                // Update the fixed to mobile price
                $currentFixedToMobilePrice->effective_to = now();
                $currentFixedToMobilePrice->save();

                $price = Price::create([
                    'entity_id' => $currentFixedToMobilePrice->entity_id,
                    'entity_type' => 'global',
                    'description' => 'fixed to mobile',
                    'amount' => $request->input('fixed_to_mobile_price'),
                    'effective_from' => now(),
                    'price_type' => 'published_price',
                ]);

                // Log the change
                AuditService::logActivity(
                    'updated',
                    'App\Models\Price',
                    $price->id,
                    'Updated fixed to mobile price from RM' . number_format($price->getOriginal('amount'), 2) . ' to RM' . number_format($request->input('fixed_to_mobile_price'), 2) . ' for subpackage ' . $subpackage->description
                );
            }

            // Check if there are any changes - check key 'fixed_to_fixed_price' and 'fixed_to_mobile_price' exist in changes array
            if (array_key_exists('fixed_to_fixed_price', $changes) || array_key_exists('fixed_to_mobile_price', $changes)) {

                // Find the latest file sequence of the day and generate file sequence
                $latest_ime_rate_pricing = ImeRatePricing::whereDate('created_at', Carbon::today())
                    ->latest()
                    ->first();
                $file_sequence = $latest_ime_rate_pricing ? str_pad($latest_ime_rate_pricing->file_sequence + 1, 3, '0', STR_PAD_LEFT) : '001';

                // Find the entry in the ImeRatePricing table with realm = 'ANY'
                $entry_realm_any = ImeRatePricing::where('realm', 'ANY')
                    ->latest()
                    ->first();

                // Define other variable
                $effective_start_date = ($entry_realm_any->effective_start_date)->format('YmdHis');
                $entry_status = 'U';
                $fixed_to_fixed_custom_price = $request->input('fixed_to_fixed_price') * 100;
                $fixed_to_mobile_custom_price = $request->input('fixed_to_mobile_price') * 100;
                $filename = 'CYPHER_IME_' . now()->format('YmdHi') . '_' . $file_sequence .'.dat';

                $new_ime_rate_pricing = ImeRatePricing::create([
                    'realm' => 'ANY',
                    'effective_start_date' => $effective_start_date,
                    'entry_status' => $entry_status,
                    'fixed_to_fixed' => $fixed_to_fixed_custom_price,
                    'fixed_to_mobile' => $fixed_to_mobile_custom_price,
                    'file_sequence' => $file_sequence,
                    'filename' => $filename,
                ]);
                
                // Log the change
                AuditService::logActivity(
                    'created',
                    get_class($new_ime_rate_pricing),
                    $new_ime_rate_pricing->id,
                    'Created new IME rate pricing file ' . $new_ime_rate_pricing->filename . ' for call usage subpackage'
                );

                // Create a new integration file in public/mediation/upload folder
                $file_content = "ANY,{$effective_start_date},,{$entry_status},{$fixed_to_fixed_custom_price},{$fixed_to_mobile_custom_price}\n";
                $file_path = public_path('mediation/upload/' . $new_ime_rate_pricing->filename);
                $file = fopen($file_path, 'w');
                // Write the header
                fwrite($file, $file_content);
                fclose($file);

                // Log activity
                AuditService::logActivity(
                    'created',
                    null,
                    null,
                    'Created file ' . $new_ime_rate_pricing->filename . ' to update price of call usage subpackage'
                );

            }
            DB::commit(); // Commit transaction if everything is successful

            return response()->json([
                'success' => true,
                'message' => 'Subpackage updated successfully',
                'data' => $subpackage
            ], 200);

        } catch (\Exception $error) {
            DB::rollBack(); // Rollback transaction on failure            
            
            return response()->json([
                'success' => false,
                'message' => `Error updating subpackage ({$error->getMessage()})`,
                'error' => $error->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    public function indexImeCallRatePrice()
    {
        return view('ott-portal.product.call-rate-price.index');
    }

    public function showImeCallRatePrice(string $id)
    {
        // dd($id);
        try {
            if ($id === 'all') {
                $imeCallRatePrices = ImeRatePricing::with(['creator', 'updater'])->get();
            } else {
                $imeCallRatePrices = ImeRatePricing::with(['creator', 'updater'])->find($id);
            }

            // Check if IME call rate prices exist
            if (empty($imeCallRatePrices)) $data = [];
            else {
                // if ($id === 'all') {
                    foreach ($imeCallRatePrices as $imeCallRatePrice) {
                        $data[] = [
                            'id' => $imeCallRatePrice->id,
                            'realm' => $imeCallRatePrice->realm,
                            'effective_start_date' => $this->reformatDateTime($imeCallRatePrice->effective_start_date),
                            'effective_end_date' => $imeCallRatePrice->effective_end_date ? $this->reformatDateTime($imeCallRatePrice->effective_end_date) : null,
                            'entry_status' => $imeCallRatePrice->entry_status,
                            'fixed_to_fixed_price' => number_format($imeCallRatePrice->fixed_to_fixed / 100, 2),
                            'fixed_to_mobile_price' => number_format($imeCallRatePrice->fixed_to_mobile / 100, 2),
                            // 'file_sequence' => $imeCallRatePrice->file_sequence,
                            'filename' => $imeCallRatePrice->filename,
                            // 'created_by' => $imeCallRatePrice->creator ? $imeCallRatePrice->creator->staff_id : 'Chyper',
                            'last_updated_by' => $imeCallRatePrice->updater ? $imeCallRatePrice->updater->staff_id : 'Chyper',
                            // 'created_at' => $this->reformatDateTime($imeCallRatePrice->created_at),
                            'last_updated_at' => $this->reformatDateTime($imeCallRatePrice->updated_at),
                        ];
                    }
                // } else {
                //     $data[] = [
                //         'id' => $imeCallRatePrices->id,
                //         'realm' => $imeCallRatePrices->realm,
                //         'effective_start_date' => $this->reformatDateTime($imeCallRatePrices->effective_start_date),
                //         'entry_status' => $imeCallRatePrices->entry_status,
                //         'fixed_to_fixed' => number_format($imeCallRatePrices->fixed_to_fixed / 100, 2),
                //         'fixed_to_mobile' => number_format($imeCallRatePrices
                //             ->fixed_to_mobile / 100, 2),                        
                //         'file_sequence' => $imeCallRatePrices->file_sequence,
                //         'filename' => $imeCallRatePrices->filename,
                //         'created_by' => $imeCallRatePrices->creator ? $imeCallRatePrices->creator->staff_id : 'Chyper',
                //         'last_updated_by' => $imeCallRatePrices->updater ? $imeCallRatePrices->updater->staff_id : 'Chyper',
                //         'created_at' => $this->reformatDateTime($imeCallRatePrices->created_at),
                //         'last_updated_at' => $this->reformatDateTime($imeCallRatePrices->updated_at),
                //     ];
                // }
            }

            return response()->json(compact('data'));
        } catch (\Exception $e) {
            return [
                'Error: ' . $e->getMessage(),
                'Line: ' . $e->getLine(),
                'File: ' . $e->getFile(),
            ];
        }
    }
}
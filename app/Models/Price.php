<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Price extends Model
{
    use HasFactory;

    protected $fillable = [
        'entity_type',
        'entity_id',
        'price_type',
        'amount',
        'description',
        'effective_from',
        'effective_to',
        'created_by',
        'updated_by'
    ];

    public function entity()
    {
        return $this->morphTo();
    }

    public function serviceItems()
    {
        return $this->hasMany(ServiceItem::class);
    }

    // Get the user who created this price
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Get the user who last updated this price
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (!$model->created_by && auth()->id()) {
                $model->created_by = auth()->id();
            }
            if (!$model->updated_by && auth()->id()) {
                $model->updated_by = auth()->id();
            }
        });

        static::updating(function ($model) {
            if (auth()->id()) {
                $model->updated_by = auth()->id();
            }
        });
    }


}
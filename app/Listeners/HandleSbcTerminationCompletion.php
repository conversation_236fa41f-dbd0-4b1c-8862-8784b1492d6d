<?php

namespace App\Listeners;

use App\Events\SbcTerminationCompleted;
use App\Models\Activity;
use App\Models\Fqdn;
use App\Models\ImeRatePricing;
use App\Models\Order;
use App\Models\PhoneNumberInventory;
use App\Models\SbcProvisioningJob;
use App\Models\Service;
use App\Models\ServiceHistory;
use App\Models\SipInterfaceIpAddress;
use App\Models\SipInterfacePublicIp;
use App\Services\AuditService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class HandleSbcTerminationCompletion
{
    private ?SbcTerminationCompleted $currentEvent = null;

    /**
     * Handle the event.
     */
    public function handle(SbcTerminationCompleted $event): void
    {
        $this->currentEvent = $event; // Store event for user ID access
        $job = $event->job;
        $result = $event->result;
        $order = Order::where('order_id', $job->order_id)->first();

        if (!$order) {
            Log::error('Order not found for completed SBC termination job', ['job_uuid' => $job->job_uuid]);
            return;
        }

        Log::info('Processing SBC termination completion', [
            'job_uuid' => $job->job_uuid,
            'order_id' => $job->order_id,
            'processing_time' => $result['processing_time'] ?? null,
            'success' => $result['success'] ?? false
        ]);

        // Record successful completion in retry history
        $job->recordRetryAttempt(
            $job->manual_retry_count > 0 ? 'manual' : 'automatic',
            'succeeded',
            null
        );

        // Process comprehensive database status updates
        $this->processComprehensiveStatusUpdates($job, $order);

        // Update FQDN, Service IP, and Public IP status after successful termination
        $this->updatePostTerminationDatabaseRecords($order, $job);

        // If this was a manual retry (activities exist), complete them
        $this->completeExistingActivities($job, $order);

        // Process IME rate pricing cancellation if customer has no active services
        $this->processImeRatePricingCancellation($order);

        // Log audit trail
        AuditService::logActivity(
            'completed',
            get_class($job),
            $job->id,
            "SBC termination completed successfully for order {$order->order_id}"
        );

        Log::info('SBC termination completion handled successfully', [
            'job_uuid' => $job->job_uuid,
            'order_id' => $job->order_id,
            'total_retry_attempts' => $job->getTotalRetryAttempts(),
            'processing_time_seconds' => $result['processing_time']
        ]);
    }

    /**
     * Process comprehensive database status updates for SBC termination completion
     */
    private function processComprehensiveStatusUpdates(SbcProvisioningJob $job, Order $order): void
    {
        try {
            DB::beginTransaction();

            // Get termination data from job
            $terminationData = $job->provisioning_data;
            if (!isset($terminationData['packages']) || empty($terminationData['packages'])) {
                Log::warning('No packages found in termination data', [
                    'job_uuid' => $job->job_uuid,
                    'order_id' => $job->order_id
                ]);
                return;
            }

            // Determine termination scenario based on current order status
            $scenario = $this->determineTerminationScenario($order);

            Log::info('Determined termination scenario', [
                'job_uuid' => $job->job_uuid,
                'order_id' => $job->order_id,
                'scenario' => $scenario,
                'current_order_status' => $order->status
            ]);

            // Process each package for comprehensive status updates
            foreach ($terminationData['packages'] as $packageData) {
                $this->processPackageTermination($order, $packageData, $scenario);
            }

            // Update order status based on scenario
            $this->updateOrderStatus($order, $scenario);

            // Update order termination status
            $order->update([
                'termination_status' => 'completed',
                'termination_completed_at' => now()
            ]);

            DB::commit();

            Log::info('Comprehensive status updates completed successfully', [
                'job_uuid' => $job->job_uuid,
                'order_id' => $job->order_id,
                'scenario' => $scenario,
                'packages_processed' => count($terminationData['packages'])
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to process comprehensive status updates', [
                'job_uuid' => $job->job_uuid,
                'order_id' => $job->order_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Determine termination scenario based on current order and service status
     */
    private function determineTerminationScenario(Order $order): string
    {
        // Check current order status to determine scenario
        if (stripos($order->status, 'progress') !== false) {
            // Scenario 1: Rollback during provisioning
            return 'rollback_provisioning';
        } elseif (stripos($order->status, 'activated') !== false) {
            // Scenario 2: Full service termination
            return 'full_termination';
        } else {
            // Default to full termination for other statuses
            Log::warning('Unknown order status for termination scenario', [
                'order_id' => $order->order_id,
                'status' => $order->status
            ]);
            return 'full_termination';
        }
    }

    /**
     * Update order status based on termination scenario
     */
    private function updateOrderStatus(Order $order, string $scenario): void
    {
        $newStatus = match ($scenario) {
            'rollback_provisioning' => 'completed rollback',
            'full_termination' => 'cancelled',
            default => 'cancelled'
        };

        $order->update(['status' => $newStatus]);

        Log::info('Updated order status', [
            'order_id' => $order->order_id,
            'scenario' => $scenario,
            'old_status' => $order->getOriginal('status'),
            'new_status' => $newStatus
        ]);
    }

    /**
     * Process package termination with comprehensive status updates
     */
    private function processPackageTermination(Order $order, array $packageData, string $scenario): void
    {
        $packageName = $packageData['package'] ?? '';
        $phoneNumbers = $packageData['phone_number'] ?? [];

        // Ensure phone numbers is an array
        if (!is_array($phoneNumbers)) {
            $phoneNumbers = [$phoneNumbers];
        }

        Log::info('Processing package termination with comprehensive status updates', [
            'order_id' => $order->order_id,
            'package' => $packageName,
            'phone_numbers' => $phoneNumbers,
            'scenario' => $scenario
        ]);

        // Get all activated services for this order and filter by package from service items
        $allServices = $order->latestServices()
            ->whereIn('status', ['activated', 'in service'])
            ->with(['serviceItems.serviceable'])
            ->get();

        // Filter services that belong to the specific package being terminated
        $services = $allServices->filter(function ($service) use ($packageName) {
            // Check if this service has a service item with the matching package
            return $service->serviceItems->contains(function ($serviceItem) use ($packageName) {
                return $serviceItem->serviceable_type === 'App\\Models\\Package'
                    && $serviceItem->description === $packageName;
                    // && $serviceItem->serviceable
                    // && $serviceItem->serviceable->name === $packageName;
            });
        });

        Log::info('Services found for package termination', [
            'order_id' => $order->order_id,
            'sbc_package_from_termination' => $packageName,
            'total_activated_services' => $allServices->count(),
            'services_matching_package' => $services->count()
        ]);

        foreach ($services as $service) {
            $this->updateServiceStatus($service, $scenario, $packageName, $order);
        }

        // Process phone number status updates
        foreach ($phoneNumbers as $phoneNumber) {
            $this->updatePhoneNumberStatus($phoneNumber, $scenario, $order);
        }
    }

    /**
     * Update service status based on termination scenario
     */
    private function updateServiceStatus($service, string $scenario, string $packageName, Order $order): void
    {
        // Update service status to deactivated
        $service->update([
            'status' => 'deactivated',
            'deactivation_date' => now()
            // Note: license_status remains unchanged as per requirements
        ]);

        // Create service history record with scenario-specific change type
        ServiceHistory::create([
            'service_id' => $service->id,
            'order_id' => $order->id ?? null,
            'quote_id' => $order->quote->id ?? null,
            'sof_id' => $order->sof->id ?? null,
            'change_type' => 'SBC deactivated',
            'effective_date' => now(),
            'updated_by' => $this->getUserIdFromEvent() ?? null
        ]);

        // Log service deactivation with audit trail
        AuditService::logActivity(
            'deactivated',
            get_class($service),
            $service->id,
            "Service deactivated via SBC termination (scenario: {$scenario}) for package: {$packageName}"
        );

        Log::info('Service status updated successfully', [
            'service_id' => $service->id,
            'package' => $packageName,
            'scenario' => $scenario,
            'status' => 'deactivated',
            'deactivation_date' => now()->toDateTimeString()
        ]);
    }

    /**
     * Update phone number inventory status based on termination scenario
     */
    private function updatePhoneNumberStatus(string $phoneNumber, string $scenario, Order $order): void
    {
        // Determine new phone number status based on scenario
        $newStatus = match ($scenario) {
            'rollback_provisioning' => 'available',
            'full_termination' => 'deactivated',
            default => 'deactivated'
        };

        // Update phone number inventory status
        $updated = PhoneNumberInventory::where('phone_number', $phoneNumber)
            ->whereIn('status', ['in use', 'in service'])
            ->update([
                'status' => $newStatus,
                'updated_at' => now()
            ]);

        if ($updated > 0) {
            Log::info('Phone number status updated', [
                'phone_number' => $phoneNumber,
                'order_id' => $order->order_id,
                'scenario' => $scenario,
                'new_status' => $newStatus
            ]);
        } else {
            Log::warning('Phone number not found or already updated', [
                'phone_number' => $phoneNumber,
                'order_id' => $order->order_id,
                'scenario' => $scenario
            ]);
        }
    }

    /**
     * Complete existing termination activities if this was a manual retry
     */
    private function completeExistingActivities(SbcProvisioningJob $job, Order $order): void
    {
        // Find existing activities for this job
        $existingActivities = Activity::where('parent_job_uuid', $job->job_uuid)
            ->whereHas('activityType', function($query) {
                // $query->whereIn('name', ['Exception - Deactivate SBC (Standard)', 'Exception - Deactivate SBC (Others)']);
                $query->whereIn('name', ['Exception - Deactivate SBC (Standard)']);
            })
            ->where('status', '!=', 'completed')
            ->get();

        if ($existingActivities->isEmpty()) {
            Log::info('No existing termination activities to complete', [
                'job_uuid' => $job->job_uuid,
                'order_id' => $job->order_id
            ]);
            return;
        }

        foreach ($existingActivities as $activity) {

            $activity->actual_duration = $activity->actual_end_date->diffInDaysFiltered(function (Carbon $date) {
                return !$date->isWeekend();
            }, $activity->actual_start_date);
            $activity->is_overdue = ($activity->actual_duration - $activity->activityType->duration > 0) ? true : false;

            $activity->update([
                'status' => 'Done',
                'actual_end_date' => now(),
                'actual_duration' => $activity->actual_end_date->diffInDaysFiltered(function (Carbon $date) {
                                            return !$date->isWeekend();
                                        }, $activity->actual_start_date),
                'is_overdue' => ($activity->actual_duration - $activity->activityType->duration > 0) ? true : false,
                'user_id' => auth()->id()
                // 'completion_notes' => 'Completed via automated SBC termination retry'
            ]);

            // Log activity completion
            AuditService::logActivity(
                'completed',
                get_class($activity),
                $activity->id,
                "SBC termination activity completed automatically for order {$order->order_id}"
            );

            Log::info('Completed existing termination activity', [
                'job_uuid' => $job->job_uuid,
                'activity_id' => $activity->activity_id,
                'activity_type' => $activity->activityType->name,
                'order_id' => $job->order_id
            ]);
        }

        Log::info('Completed all existing termination activities', [
            'job_uuid' => $job->job_uuid,
            'order_id' => $job->order_id,
            'completed_activities_count' => $existingActivities->count()
        ]);
    }

    /**
     * Get the user ID from the current event result
     */
    private function getUserIdFromEvent(): ?int
    {
        return $this->currentEvent?->result['user_id'] ?? null;
    }

    /**
     * Update FQDN, Service IP, and Public IP status after successful termination
     * Inverse operations of the provisioning completion handler - releases resources back to available pool
     *
     * @param Order $order
     * @param SbcProvisioningJob $job
     * @return void
     */
    private function updatePostTerminationDatabaseRecords(Order $order, SbcProvisioningJob $job): void
    {
        try {
            Log::info('Starting post-termination database updates', [
                'order_id' => $order->order_id,
                'job_uuid' => $job->job_uuid
            ]);

            // Get services for this order to determine package types
            $services = $order->latestServices;

            $fqdnUpdates = 0;
            $serviceIpUpdates = 0;
            $publicIpUpdates = 0;

            // Update FQDN status - release resources back to available pool
            $fqdnRecords = Fqdn::where('order_id', $order->id)
                ->whereIn('status', ['assigned', 'reserved', 'in service'])
                ->get();

            foreach ($fqdnRecords as $fqdn) {
                $fqdn->update([
                    'order_id' => null,
                    'customer_id' => null,
                    'status' => 'available'
                ]);
                $fqdnUpdates++;

                Log::info('Released FQDN after SBC termination', [
                    'order_id' => $order->order_id,
                    'fqdn_id' => $fqdn->id,
                    'package' => $fqdn->package,
                    'hostname' => $fqdn->hostname,
                    'subdomain' => $fqdn->subdomain,
                    'status' => 'available'
                ]);
            }

            // Check if any services have Zoom packages for IP status updates
            $hasZoomPackage = $services->contains(function ($service) {
                return $service->serviceItems->contains(function ($serviceItem) {
                    return stripos($serviceItem->description, 'zoom') !== false;
                });
            });

            if ($hasZoomPackage) {
                // Update Service IP status - release resources back to available pool
                $serviceIpUpdates = SipInterfaceIpAddress::where('order_id', $order->id)
                    ->where('status', 'assigned')
                    ->update([
                        'order_id' => null,
                        'customer_id' => null,
                        'status' => 'available'
                    ]);

                // Update Public IP status - release resources back to available pool
                $publicIpUpdates = SipInterfacePublicIp::where('order_id', $order->id)
                    ->where('status', 'assigned')
                    ->update([
                        'order_id' => null,
                        'customer_id' => null,
                        'status' => 'available'
                    ]);

                Log::info('Released IP resources for Zoom packages after SBC termination', [
                    'order_id' => $order->order_id,
                    'service_ips_released' => $serviceIpUpdates,
                    'public_ips_released' => $publicIpUpdates
                ]);
            }

            // Log summary of all updates
            Log::info('Post-termination database updates completed', [
                'order_id' => $order->order_id,
                'job_uuid' => $job->job_uuid,
                'fqdn_releases' => $fqdnUpdates,
                'service_ip_releases' => $serviceIpUpdates,
                'public_ip_releases' => $publicIpUpdates,
                'has_zoom_package' => $hasZoomPackage
            ]);

        } catch (\Exception $e) {
            Log::error('Error updating post-termination database records', [
                'order_id' => $order->order_id,
                'job_uuid' => $job->job_uuid,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Don't throw the exception to avoid breaking the main termination completion flow
            // The termination was successful, these updates are supplementary
        }
    }

    /**
     * Process IME rate pricing cancellation when customer has no active services
     * This method orchestrates the 4-step validation and cancellation process
     */
    private function processImeRatePricingCancellation(Order $order): void
    {
        try {
            Log::info('Starting IME rate pricing cancellation process', [
                'order_id' => $order->order_id,
                'customer_id' => $order->customer->id ?? null
            ]);

            // Step 1: Service Status Validation
            if ($this->hasActiveServices($order)) {
                Log::info('Customer has active services, skipping IME rate pricing cancellation', [
                    'order_id' => $order->order_id,
                    'customer_id' => $order->customer->id
                ]);
                return;
            }

            // Step 2: Custom Price Entry Lookup
            $activeEntry = $this->findActiveImeRatePricingEntry($order);
            if (!$activeEntry) {
                Log::info('No active IME rate pricing entry found, skipping cancellation', [
                    'order_id' => $order->order_id,
                    'customer_id' => $order->customer->id
                ]);
                return;
            }

            // Step 3: Create Cancellation Entry
            $cancellationEntry = $this->createImeRatePricingCancellationEntry($activeEntry, $order);
            if (!$cancellationEntry) {
                Log::error('Failed to create cancellation entry, aborting IME rate pricing cancellation', [
                    'order_id' => $order->order_id,
                    'active_entry_id' => $activeEntry->id
                ]);
                return;
            }

            // Step 4: Generate IME Rate Pricing File
            $this->createImeRatePricingCancellationFile($cancellationEntry, $order);

            // Log audit trail
            AuditService::logActivity(
                'created',
                get_class($cancellationEntry),
                $cancellationEntry->id,
                "Created IME rate pricing cancellation file {$cancellationEntry->filename} for customer {$order->customer->name} via automated SBC termination"
            );

            Log::info('IME rate pricing cancellation process completed successfully', [
                'order_id' => $order->order_id,
                'customer_id' => $order->customer->id,
                'cancellation_entry_id' => $cancellationEntry->id,
                'filename' => $cancellationEntry->filename
            ]);

        } catch (\Exception $e) {
            Log::error('IME rate pricing cancellation process failed', [
                'order_id' => $order->order_id,
                'customer_id' => $order->customer->id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Don't throw the exception to avoid breaking the main termination completion flow
            // The termination was successful, IME cancellation is supplementary
        }
    }

    /**
     * Step 1: Check if customer has any active services
     * Active services are those with status: 'in service', 'activated', or 'pending activation'
     */
    private function hasActiveServices(Order $order): bool
    {
        $customer = $order->customer;
        if (!$customer) {
            Log::warning('Customer not found for order', ['order_id' => $order->order_id]);
            return false;
        }

        $activeStatuses = ['in service', 'activated', 'pending activation'];

        $activeServicesCount = Service::where('customer_id', $customer->id)
            ->whereIn('status', $activeStatuses)
            ->count();

        Log::info('Active services check for IME rate pricing cancellation', [
            'order_id' => $order->order_id,
            'customer_id' => $customer->id,
            'customer_name' => $customer->name,
            'active_services_count' => $activeServicesCount,
            'checked_statuses' => $activeStatuses
        ]);

        return $activeServicesCount > 0;
    }

    /**
     * Step 2: Find active IME rate pricing entry for customer
     */
    private function findActiveImeRatePricingEntry(Order $order): ?ImeRatePricing
    {
        $customer = $order->customer;
        if (!$customer || !$customer->brn) {
            Log::warning('Customer or BRN not found for IME rate pricing lookup', [
                'order_id' => $order->order_id,
                'customer_id' => $customer->id ?? null
            ]);
            return null;
        }

        // Generate realm using same logic as provisioning
        $realm = 'tmims-' . $this->sanitizeBrn($customer->brn);

        $activeEntry = ImeRatePricing::where('realm', $realm)
            ->whereIn('entry_status', ['A', 'U'])
            ->latest()
            ->first();

        Log::info('IME rate pricing entry lookup', [
            'order_id' => $order->order_id,
            'customer_brn' => $customer->brn,
            'realm' => $realm,
            'active_entry_found' => !is_null($activeEntry),
            'entry_id' => $activeEntry->id ?? null
        ]);

        return $activeEntry;
    }

    /**
     * Step 3: Create cancellation entry by duplicating active entry
     */
    private function createImeRatePricingCancellationEntry(
        ImeRatePricing $activeEntry,
        Order $order
    ): ?ImeRatePricing {
        try {
            // Generate file sequence (same logic as provisioning)
            $latestImeRatePricing = ImeRatePricing::whereDate('created_at', Carbon::today())
                ->latest()
                ->first();
            $fileSequence = $latestImeRatePricing ?
                str_pad($latestImeRatePricing->file_sequence + 1, 3, '0', STR_PAD_LEFT) : '001';

            // Generate filename
            $filename = 'CYPHER_IME_' . now()->format('YmdHi') . '_' . $fileSequence . '.dat';

            // Create cancellation entry
            $cancellationEntry = ImeRatePricing::create([
                'realm' => $activeEntry->realm,
                'effective_start_date' => $activeEntry->effective_start_date,
                'effective_end_date' => now(), // Set end date to current timestamp
                'entry_status' => 'D', // Deactivated/cancelled
                'fixed_to_fixed' => $activeEntry->fixed_to_fixed,
                'fixed_to_mobile' => $activeEntry->fixed_to_mobile,
                'file_sequence' => $fileSequence,
                'filename' => $filename,
            ]);

            Log::info('Created IME rate pricing cancellation entry', [
                'order_id' => $order->order_id,
                'original_entry_id' => $activeEntry->id,
                'cancellation_entry_id' => $cancellationEntry->id,
                'realm' => $activeEntry->realm,
                'filename' => $filename,
                'effective_end_date' => now()->toDateTimeString()
            ]);

            return $cancellationEntry;

        } catch (\Exception $e) {
            Log::error('Failed to create IME rate pricing cancellation entry', [
                'order_id' => $order->order_id,
                'active_entry_id' => $activeEntry->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Step 4: Generate IME rate pricing cancellation file
     */
    private function createImeRatePricingCancellationFile(
        ImeRatePricing $cancellationEntry,
        Order $order
    ): void {
        try {
            // Create file content (same format as provisioning)
            $fileContent = sprintf(
                "%s,%s,%s,%s,%d,%d\n",
                $cancellationEntry->realm,
                $cancellationEntry->effective_start_date->format('YmdHis'),
                $cancellationEntry->effective_end_date->format('YmdHis'), // Include end date for cancellation
                $cancellationEntry->entry_status,
                $cancellationEntry->fixed_to_fixed,
                $cancellationEntry->fixed_to_mobile
            );

            // Create file path
            $filePath = public_path('mediation/upload/' . $cancellationEntry->filename);

            // Write file
            $file = fopen($filePath, 'w');
            if ($file === false) {
                throw new \Exception('Failed to open file for writing: ' . $filePath);
            }

            fwrite($file, $fileContent);
            fclose($file);

            // Set file permissions
            chmod($filePath, 0775);

            // Change file ownership (same as provisioning)
            shell_exec("sudo chown 1002:www-data " . escapeshellarg($filePath));

            Log::info('Created IME rate pricing cancellation file', [
                'order_id' => $order->order_id,
                'filename' => $cancellationEntry->filename,
                'file_path' => $filePath,
                'file_content' => trim($fileContent)
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to create IME rate pricing cancellation file', [
                'order_id' => $order->order_id,
                'filename' => $cancellationEntry->filename,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Sanitize BRN for use in filenames by replacing problematic characters
     * (Same implementation as HandleSbcProvisioningCompletion)
     */
    private function sanitizeBrn(string $brn): string
    {
        return str_replace(['/', '\\', ':', '*', '?', '"', '<', '>', '|'], '-', $brn);
    }
}
